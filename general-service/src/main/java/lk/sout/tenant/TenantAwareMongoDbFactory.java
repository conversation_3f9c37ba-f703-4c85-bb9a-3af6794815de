package lk.sout.tenant;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;

/**
 * Tenant-aware MongoDB database factory
 * 
 * Extends SimpleMongoClientDatabaseFactory to dynamically determine
 * the database based on the current tenant context from TenantContextFilter.
 * 
 * This allows a single Spring Boot application to serve multiple tenants
 * with separate databases based on the subdomain of the request.
 */
public class TenantAwareMongoDbFactory extends SimpleMongoClientDatabaseFactory {

    private static final Logger logger = LoggerFactory.getLogger(TenantAwareMongoDbFactory.class);
    
    private final String fallbackDatabase;

    /**
     * Constructor
     * 
     * @param mongoClient MongoDB client instance
     * @param fallbackDatabase default database name to use when tenant context is not available
     */
    public TenantAwareMongoDbFactory(MongoClient mongoClient, String fallbackDatabase) {
        super(mongoClient, fallbackDatabase);
        this.fallbackDatabase = fallbackDatabase;
        logger.info("🏗️ TenantAwareMongoDbFactory initialized with fallback database: {}", fallbackDatabase);
    }

    /**
     * Get MongoDatabase for current tenant
     * 
     * This method is called by Spring Data MongoDB for every database operation.
     * It dynamically determines which database to use based on the current tenant context.
     * 
     * @return MongoDatabase instance for the current tenant
     */
    @Override
    public MongoDatabase getMongoDatabase() {
        String databaseName = getCurrentDatabaseName();
        
        logger.debug("🗄️ Current tenant DB: {}", databaseName);
        
        // Get database from MongoDB client
        MongoDatabase database = getMongoClient().getDatabase(databaseName);
        
        return database;
    }

    /**
     * Determine the current database name based on tenant context
     * 
     * @return database name for current tenant or fallback database
     */
    private String getCurrentDatabaseName() {
        try {
            // Try to get database from tenant context
            String tenantDatabase = TenantContext.getCurrentDatabase();
            
            if (tenantDatabase != null && !tenantDatabase.trim().isEmpty()) {
                return tenantDatabase;
            }
            
        } catch (Exception e) {
            logger.warn("⚠️ Error getting tenant database, using fallback: {}", e.getMessage());
        }
        
        // Fallback to default database
        logger.debug("🔄 Using fallback database: {}", fallbackDatabase);
        return fallbackDatabase;
    }


}
