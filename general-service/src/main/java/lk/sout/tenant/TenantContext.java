package lk.sout.tenant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * ThreadLocal holder for current tenant context
 *
 * Stores the current tenant identifier extracted from the request
 * and provides methods to access it during database resolution.
 */
@Component
public class TenantContext {

    private static final Logger logger = LoggerFactory.getLogger(TenantContext.class);

    private static final ThreadLocal<String> currentTenant = new ThreadLocal<>();

    private static String defaultDatabase;

    /**
     * Constructor to inject the default database from application.properties
     */
    public TenantContext(@Value("${spring.data.mongodb.database}") String defaultDatabase) {
        TenantContext.defaultDatabase = defaultDatabase;
        logger.info("🔧 TenantContext initialized with default database: {}", defaultDatabase);
    }
    
    /**
     * Set the current tenant for this thread
     * 
     * @param tenant tenant identifier (subdomain)
     */
    public static void setCurrentTenant(String tenant) {
        currentTenant.set(tenant);
        logger.trace("🔧 Set current tenant: {}", tenant);
    }
    
    /**
     * Get the current tenant for this thread
     * 
     * @return tenant identifier or "default" if not set
     */
    public static String getCurrentTenant() {
        String tenant = currentTenant.get();
        if (tenant == null || tenant.trim().isEmpty()) {
            logger.debug("⚠️ No tenant in context, returning default");
            return "default";
        }
        return tenant;
    }
    
    /**
     * Clear the current tenant context
     * Should be called in finally block to prevent memory leaks
     */
    public static void clear() {
        String tenant = currentTenant.get();
        currentTenant.remove();
        logger.trace("🧹 Cleared tenant context: {}", tenant);
    }
    
    /**
     * Get the database name for the current tenant
     *
     * Maps tenant identifier to actual database name:
     * - "default" -> configured default database (from application.properties)
     * - "demo" -> defaultDatabase + "Demo" (e.g., "generalWebDemo")
     * - "wanigarathna" -> defaultDatabase + "Wanigarathna" (e.g., "generalWebWanigarathna")
     * - etc.
     *
     * @return database name for current tenant
     */
    public static String getCurrentDatabase() {
        String tenant = getCurrentTenant();

        if ("default".equals(tenant)) {
            return defaultDatabase != null ? defaultDatabase : "generalWeb";
        }

        // Get base database name (remove any existing suffix if present)
        String baseDatabaseName = getBaseDatabaseName();

        // Capitalize first letter for database name
        String capitalizedTenant = tenant.substring(0, 1).toUpperCase() + tenant.substring(1).toLowerCase();
        String databaseName = baseDatabaseName + capitalizedTenant;

        logger.trace("🗄️ Mapped tenant '{}' to database '{}'", tenant, databaseName);
        return databaseName;
    }

    /**
     * Get the base database name for tenant database generation
     *
     * @return base database name (e.g., "generalWeb")
     */
    private static String getBaseDatabaseName() {
        if (defaultDatabase == null) {
            return "generalWeb";
        }

        // If the default database already has a suffix, remove it to get the base name
        // This handles cases like "generalWebAjstore" -> "generalWeb"
        if (defaultDatabase.startsWith("generalWeb") && defaultDatabase.length() > "generalWeb".length()) {
            return "generalWeb";
        }

        return defaultDatabase;
    }
}
