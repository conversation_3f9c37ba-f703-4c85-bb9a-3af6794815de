package lk.sout.tenant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * Filter to extract tenant information from subdomain and store in ThreadLocal
 * 
 * Extracts subdomain from Host header (e.g., "abc" from "abc.viganana.com")
 * and stores it in TenantContext for use during database resolution.
 * 
 * Examples:
 * - demo.viganana.com -> tenant: "demo" -> database: "generalWebDemo"
 * - wanigarathna.viganana.com -> tenant: "wanigarathna" -> database: "generalWebWanigarathna"
 * - localhost:8080 -> tenant: "default" -> database: "generalWeb"
 */
@Order(1) // Execute early in filter chain
public class TenantContextFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(TenantContextFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        logger.info("🏢 TenantContextFilter initialized - Multi-tenant mode enabled");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        
        try {
            // Extract tenant from Host header
            String tenant = extractTenantFromHost(httpRequest);
            
            // Store in ThreadLocal
            TenantContext.setCurrentTenant(tenant);
            
            logger.debug("🔍 Request: {} | Host: {} | Tenant: {}", 
                httpRequest.getRequestURI(), 
                httpRequest.getHeader("Host"), 
                tenant);
            
            // Continue with request processing
            chain.doFilter(request, response);
            
        } finally {
            // Always clear ThreadLocal to prevent memory leaks
            TenantContext.clear();
        }
    }

    @Override
    public void destroy() {
        logger.info("🏢 TenantContextFilter destroyed");
    }

    /**
     * Extract tenant identifier from Host header
     * 
     * @param request HTTP request
     * @return tenant identifier (subdomain or "default")
     */
    private String extractTenantFromHost(HttpServletRequest request) {
        String host = request.getHeader("Host");
        
        if (host == null || host.trim().isEmpty()) {
            logger.warn("⚠️ No Host header found, using default tenant");
            return "default";
        }
        
        // Remove port if present (e.g., "localhost:8080" -> "localhost")
        if (host.contains(":")) {
            host = host.substring(0, host.indexOf(":"));
        }
        
        // Handle localhost and IP addresses
        if (host.equals("localhost") || host.matches("\\d+\\.\\d+\\.\\d+\\.\\d+")) {
            logger.debug("🏠 Localhost/IP detected: {}, using default tenant", host);
            return "default";
        }
        
        // Extract subdomain from host (e.g., "demo.viganana.com" -> "demo")
        String[] parts = host.split("\\.");
        
        if (parts.length >= 2) {
            String subdomain = parts[0];
            
            // Validate subdomain (only alphanumeric and hyphens)
            if (subdomain.matches("[a-zA-Z0-9-]+")) {
                logger.debug("✅ Extracted tenant: {} from host: {}", subdomain, host);
                return subdomain;
            } else {
                logger.warn("⚠️ Invalid subdomain format: {}, using default tenant", subdomain);
                return "default";
            }
        }
        
        logger.warn("⚠️ Could not extract subdomain from host: {}, using default tenant", host);
        return "default";
    }
}
