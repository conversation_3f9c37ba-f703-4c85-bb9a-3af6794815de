package lk.sout.tenant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * Test controller to verify multi-tenant functionality
 * 
 * Provides endpoints to check current tenant context and database connectivity.
 * This controller can be used to test that tenant resolution is working correctly.
 */
@RestController
@RequestMapping("/api/tenant")
public class TenantTestController {

    private static final Logger logger = LoggerFactory.getLogger(TenantTestController.class);

    @Autowired
    private MongoTemplate mongoTemplate;

    @Value("${spring.data.mongodb.database}")
    private String configuredDefaultDatabase;

    /**
     * Get current tenant information
     * 
     * Returns details about the current tenant context including:
     * - Host header from request
     * - Resolved tenant identifier
     * - Database name being used
     * - Database connectivity status
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getTenantInfo(HttpServletRequest request) {
        Map<String, Object> info = new HashMap<>();
        
        try {
            // Get request information
            String host = request.getHeader("Host");
            String currentTenant = TenantContext.getCurrentTenant();
            String currentDatabase = TenantContext.getCurrentDatabase();
            
            info.put("host", host);
            info.put("tenant", currentTenant);
            info.put("database", currentDatabase);
            info.put("configuredDefaultDatabase", configuredDefaultDatabase);
            info.put("requestUri", request.getRequestURI());
            
            // Test database connectivity
            try {
                String actualDatabaseName = mongoTemplate.getDb().getName();
                info.put("actualDatabase", actualDatabaseName);
                info.put("databaseConnected", true);
                
                // Get collection count as a simple test
                long collectionCount = mongoTemplate.getCollectionNames().size();
                info.put("collectionCount", collectionCount);
                
            } catch (Exception e) {
                info.put("databaseConnected", false);
                info.put("databaseError", e.getMessage());
                logger.error("❌ Database connectivity test failed: {}", e.getMessage());
            }
            
            info.put("status", "success");
            
            logger.info("🔍 Tenant info requested - Host: {}, Tenant: {}, Database: {}", 
                host, currentTenant, currentDatabase);
            
        } catch (Exception e) {
            info.put("status", "error");
            info.put("error", e.getMessage());
            logger.error("❌ Error getting tenant info: {}", e.getMessage());
        }
        
        return ResponseEntity.ok(info);
    }

    /**
     * Test database operation with current tenant
     * 
     * Performs a simple database operation to verify that the correct
     * tenant database is being used.
     */
    @GetMapping("/test-db")
    public ResponseEntity<Map<String, Object>> testDatabase() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String currentTenant = TenantContext.getCurrentTenant();
            String currentDatabase = TenantContext.getCurrentDatabase();
            
            // Get actual database name from MongoDB
            String actualDatabaseName = mongoTemplate.getDb().getName();
            
            // Test basic database operation
            boolean databaseExists = mongoTemplate.getDb().listCollectionNames().iterator().hasNext();
            
            result.put("tenant", currentTenant);
            result.put("expectedDatabase", currentDatabase);
            result.put("actualDatabase", actualDatabaseName);
            result.put("databaseExists", databaseExists);
            result.put("databaseMatch", currentDatabase.equals(actualDatabaseName));
            result.put("status", "success");
            
            logger.info("🧪 Database test - Tenant: {}, Expected: {}, Actual: {}, Match: {}", 
                currentTenant, currentDatabase, actualDatabaseName, 
                currentDatabase.equals(actualDatabaseName));
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("error", e.getMessage());
            logger.error("❌ Database test failed: {}", e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
}
