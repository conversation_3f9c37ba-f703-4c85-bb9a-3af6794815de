package lk.sout.tenant;

import jakarta.servlet.Filter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * Configuration for multi-tenant functionality
 * 
 * This configuration ensures that the TenantContextFilter is properly
 * registered and executed early in the filter chain.
 */
@Configuration
public class TenantConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(TenantConfiguration.class);

    /**
     * Register TenantContextFilter with high priority
     * 
     * This ensures the filter runs early in the chain to set up
     * tenant context before any database operations.
     */
    @Bean
    public FilterRegistrationBean<Filter> tenantContextFilter() {
        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();
        
        registrationBean.setFilter(new TenantContextFilter());
        registrationBean.addUrlPatterns("/*"); // Apply to all URLs
        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE); // Execute first
        registrationBean.setName("tenantContextFilter");
        
        logger.info("🔧 Registered TenantContextFilter with highest precedence");
        
        return registrationBean;
    }
}
