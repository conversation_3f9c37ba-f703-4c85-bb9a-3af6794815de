package lk.sout.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lk.sout.event.CascadeSaveMongoEventListener;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.MongoTransactionManager;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.context.annotation.Primary;

/**
 * Created by Madhawa Weerasinghe on 7/27/2017.
 */
@Configuration
@EnableTransactionManagement
@EnableMongoAuditing
@ComponentScan(basePackages = {"lk.sout.core", "lk.sout.general"})
@EnableMongoRepositories(basePackages = {"lk.sout.core.repository", "lk.sout.general.inventory.repository",
        "lk.sout.general.hr.repository", "lk.sout.general.trade.repository", "lk.sout.general.dashboard.repository"})

public class DbConfig extends AbstractMongoClientConfiguration {

    @Bean
    public CascadeSaveMongoEventListener cascadingMongoEventListener() {
        return new CascadeSaveMongoEventListener();
    }

    @Bean
    public AuditorAware<String> auditorProvider() {
        return new SpringSecurityAuditAwareImpl();
    }

    @Bean
    @Qualifier("mongoDbFactory")
    public MongoDatabaseFactory mongoDbFactory(@Qualifier("defaultMongoClient") MongoClient mongoClient) {
        // Create simple database factory
        return new SimpleMongoClientDatabaseFactory(mongoClient, getDatabaseName());
    }

    @Bean
    MongoTransactionManager transactionManager(@Qualifier("mongoDbFactory") MongoDatabaseFactory dbFactory) {
        return new MongoTransactionManager(dbFactory);
    }

    @Value("${spring.data.mongodb.host}")
    private String host;

    @Value("${spring.data.mongodb.database}")
    private String database;

    @Value("${spring.data.mongodb.authDatabase}")
    private String authDb;

    @Value("${spring.data.mongodb.port}")
    private int port;

    @Value("${spring.data.mongodb.username}")
    private String username;

    @Value("${spring.data.mongodb.password}")
    private String password;

    @Override
    protected String getDatabaseName() {
        // Return configured database name
        return database;
    }

    @Override
    @Bean("defaultMongoClient")
    public MongoClient mongoClient() {
        // Create connection without specifying database (will be determined per request)
        final ConnectionString connectionString = new ConnectionString("mongodb://" + host + ":" + port);
        final MongoClientSettings mongoClientSettings = MongoClientSettings.builder()
                .applyConnectionString(connectionString)
                .credential(MongoCredential.createCredential(username, authDb, password.toCharArray()))
                .build();
        return MongoClients.create(mongoClientSettings);
    }

    @Bean
    @Primary
    public MongoClient mongoClientPrimary(@Qualifier("defaultMongoClient") MongoClient mongoClient) {
        // Provide unqualified MongoClient bean for other services like backup
        return mongoClient;
    }

    @Bean
    @Primary
    public MongoTemplate mongoTemplate(@Qualifier("mongoDbFactory") MongoDatabaseFactory dbFactory) {
        // Use standard database factory
        return new MongoTemplate(dbFactory);
    }

    @Override
    protected boolean autoIndexCreation() {
        return true;
    }

}
