package lk.sout.general.inventory.entity;

import lk.sout.general.trade.entity.Supplier;
import org.springframework.data.annotation.*;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Created by Madhawa Weerasinghe on 4/20/2018
 */
@Document
@Component
public class Item {

    @Id
    private String id;

    @Indexed(unique = true)
    private String itemCode;

    @Indexed(unique = true)
    private String barcode;

    private String supplierCode;

    private String itemName;

    private String description;

    // only use to save initial stock quantity in main store
    //@Transient
    private double quantity;

    @DBRef
    private ItemType itemType;

    @DBRef
    private Brand brand;

    @DBRef
    private Model model;

    @DBRef
    private ItemCategory itemCategory;

    @DBRef
    private Supplier supplier;

    private double sellingPrice;

    private double itemCost;

    private double retailDiscount;

    private double wholesaleDiscount;

    private double caseQuantity;

    @DBRef
    private UOM uom;

    @DBRef
    private Rack rack;

    private double deadStockLevel;

    private boolean manageStock;

    private boolean active;

    private boolean wholesale;

    private boolean retail;

    // Serial number management for phone shops
    private boolean manageSerial;

    @CreatedDate
    private Date createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private Date lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getItemCode() {
        return itemCode;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public ItemType getItemType() {
        return itemType;
    }

    public void setItemType(ItemType itemType) {
        this.itemType = itemType;
    }

    public Model getModel() {
        return model;
    }

    public void setModel(Model model) {
        this.model = model;
    }

    public Brand getBrand() {
        return brand;
    }

    public void setBrand(Brand brand) {
        this.brand = brand;
    }

    public ItemCategory getItemCategory() {
        return itemCategory;
    }

    public void setItemCategory(ItemCategory itemCategory) {
        this.itemCategory = itemCategory;
    }

    public Double getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(Double sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public Double getItemCost() {
        return itemCost;
    }

    public void setItemCost(Double itemCost) {
        this.itemCost = itemCost;
    }

    public Double getRetailDiscount() {
        return retailDiscount;
    }

    public void setRetailDiscount(Double retailDiscount) {
        this.retailDiscount = retailDiscount;
    }

    public Double getWholesaleDiscount() {
        return wholesaleDiscount;
    }

    public void setWholesaleDiscount(Double wholesaleDiscount) {
        this.wholesaleDiscount = wholesaleDiscount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double getDeadStockLevel() {
        return deadStockLevel;
    }

    public void setDeadStockLevel(Double deadStockLevel) {
        this.deadStockLevel = deadStockLevel;
    }

    public Double getCaseQuantity() {
        return caseQuantity;
    }

    public void setCaseQuantity(Double caseQuantity) {
        this.caseQuantity = caseQuantity;
    }

    public UOM getUom() {
        return uom;
    }

    public void setUom(UOM uom) {
        this.uom = uom;
    }

    public Rack getRack() {
        return rack;
    }

    public void setRack(Rack rack) {
        this.rack = rack;
    }

    public boolean isManageStock() {
        return manageStock;
    }

    public void setManageStock(boolean manageStock) {
        this.manageStock = manageStock;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public Supplier getSupplier() {
        return supplier;
    }

    public void setSupplier(Supplier supplier) {
        this.supplier = supplier;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public boolean isWholesale() {
        return wholesale;
    }

    public void setWholesale(boolean wholesale) {
        this.wholesale = wholesale;
    }

    public boolean isRetail() {
        return retail;
    }

    public void setRetail(boolean retail) {
        this.retail = retail;
    }

    public boolean isManageSerial() {
        return manageSerial;
    }

    public void setManageSerial(boolean manageSerial) {
        this.manageSerial = manageSerial;
    }

}

