// MongoDB script to create user for ajstore database
// Run this script with: mongo ajstore setup-ajstore-user.js

print("🔧 Setting up user for ajstore database...");

// Switch to ajstore database
use ajstore;

// Check if user already exists
var existingUser = db.getUser("generalWeb");
if (existingUser) {
    print("⚠️ User 'generalWeb' already exists in ajstore database");
    print("Current roles:", JSON.stringify(existingUser.roles, null, 2));
} else {
    print("📝 Creating user 'generalWeb' in ajstore database...");
    
    // Create the user with appropriate permissions
    db.createUser({
        user: "generalWeb",
        pwd: "awer@#$cdfDDF!@S_+(", 
        roles: [
            { role: "readWrite", db: "ajstore" },
            { role: "dbAdmin", db: "ajstore" }
        ]
    });
    
    print("✅ User 'generalWeb' created successfully in ajstore database");
}

// Verify the user can authenticate
try {
    db.auth("generalWeb", "awer@#$cdfDDF!@S_+(");
    print("✅ Authentication test successful");
    
    // Test basic operations
    var testResult = db.runCommand({ping: 1});
    if (testResult.ok === 1) {
        print("✅ Database connection test successful");
    }
    
    // Show collections (if any)
    var collections = db.getCollectionNames();
    print("📊 Collections in ajstore database:", collections.length);
    if (collections.length > 0) {
        print("   Collections:", collections.join(", "));
    }
    
} catch (e) {
    print("❌ Authentication test failed:", e.message);
}

print("🎉 Setup complete!");
print("");
print("📋 Configuration for application.properties:");
print("spring.data.mongodb.database=ajstore");
print("spring.data.mongodb.authDatabase=ajstore");
print("spring.data.mongodb.username=generalWeb");
print("spring.data.mongodb.password=awer@#$cdfDDF!@S_+(");
