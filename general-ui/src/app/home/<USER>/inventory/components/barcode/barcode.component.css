/* Sticker container layout */
.sticker-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  padding: 0.5mm;
}

/* Barcode section */
.barcode-section {
  flex: 0 0 auto;
  width: 100%;
  text-align: center;
  margin-bottom: 0.5mm;
}

/* Item info section */
.item-info-section {
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
}

.item-name {
  font-size: 0.7em;
  font-weight: bold;
  margin-bottom: 0.2mm;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.1;
}

.item-code {
  font-size: 0.6em;
  font-weight: bold;
  margin-bottom: 0.2mm;
  line-height: 1.1;
}

.barcode-text {
  font-size: 0.5em;
  font-family: monospace;
  font-weight: bold;
  line-height: 1.1;
  margin-bottom: 0.2mm;
}

/* Price section */
.price-section {
  flex: 0 0 auto;
  width: 100%;
  text-align: center;
  margin-top: 0.3mm;
}

.price {
  font-size: 0.7em;
  font-weight: bold;
  padding: 0.2mm;
  border-top: 1px solid #ddd;
  line-height: 1.1;
}

/* Print-specific styles */
@media print {
  .sticker-container {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  #print-section > div {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    gap: 5mm !important;
    padding: 0 !important;
    margin: 0 !important;
    justify-content: flex-start !important;
    align-items: flex-start !important;
    width: 100% !important;
    height: auto !important;
  }

  #print-section > div > div {
    flex-shrink: 0 !important;
    margin-right: 5mm !important;
  }
}

/* Responsive adjustments for different paper sizes */
@media (max-width: 30mm) {
  .item-name { font-size: 0.6em; line-height: 1.0; }
  .item-code { font-size: 0.5em; line-height: 1.0; }
  .price { font-size: 0.6em; line-height: 1.0; }
  .barcode-text { font-size: 0.4em; line-height: 1.0; }
  .sticker-container { padding: 0.3mm; }
}

@media (min-width: 50mm) {
  .item-name { font-size: 0.8em; line-height: 1.2; }
  .item-code { font-size: 0.7em; line-height: 1.2; }
  .price { font-size: 0.8em; line-height: 1.2; }
  .barcode-text { font-size: 0.6em; line-height: 1.2; }
  .sticker-container { padding: 0.8mm; }
}
