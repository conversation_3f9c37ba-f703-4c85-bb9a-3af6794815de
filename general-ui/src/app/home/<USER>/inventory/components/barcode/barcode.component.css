/* Sticker container layout */
.sticker-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  padding: 1mm;
}

/* Barcode section */
.barcode-section {
  flex: 0 0 auto;
  width: 100%;
  text-align: center;
  margin-bottom: 1mm;
}

/* Item info section */
.item-info-section {
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
}

.item-name {
  font-size: 0.8em;
  font-weight: bold;
  margin-bottom: 0.5mm;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-code {
  font-size: 0.6em;
  font-weight: bold;
  margin-bottom: 0.5mm;
}

.barcode-text {
  font-size: 0.6em;
  font-family: monospace;
  font-weight: bold;
}

/* Price section */
.price-section {
  flex: 0 0 auto;
  width: 100%;
  text-align: center;
  margin-top: 1mm;
}

.price {
  font-size: 0.8em;
  font-weight: bold;
  padding: 0.5mm;
  border-top: 1px solid #ddd;
}

/* Print-specific styles */
@media print {
  .sticker-container {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  #print-section > div {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    gap: 5mm !important;
    padding: 0 !important;
    margin: 0 !important;
    justify-content: flex-start !important;
    align-items: flex-start !important;
    width: 100% !important;
    height: auto !important;
  }

  #print-section > div > div {
    flex-shrink: 0 !important;
    margin-right: 5mm !important;
  }
}

/* Responsive adjustments for different paper sizes */
@media (max-width: 30mm) {
  .item-name { font-size: 0.7em; }
  .item-code { font-size: 0.5em; }
  .price { font-size: 0.7em; }
}

@media (min-width: 50mm) {
  .item-name { font-size: 0.9em; }
  .item-code { font-size: 0.7em; }
  .price { font-size: 0.9em; }
}
