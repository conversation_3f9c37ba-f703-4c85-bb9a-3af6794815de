import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { GeneralSettingsService } from '../../admin/service/general-settings.service';

export interface BarcodeSettings {
  useCostCodes: boolean;
  showBarcode: boolean;
  columns: number;
  paperSize: string;
}

export interface CostCodeSettings {
  letterMapping: { [key: string]: string }; // Maps numbers 0-9 to letters
}

@Injectable({
  providedIn: 'root'
})
export class BarcodeSettingsService {

  private readonly BARCODE_SETTINGS_KEY = 'barcodeSettings';
  private readonly COST_CODE_SETTINGS_KEY = 'costCodeSettings';

  constructor(private generalSettingsService: GeneralSettingsService) { }

  /**
   * Get barcode settings from localStorage with defaults
   */
  getBarcodeSettings(): BarcodeSettings {
    try {
      const saved = localStorage.getItem(this.BARCODE_SETTINGS_KEY);
      if (saved) {
        return { ...this.getDefaultBarcodeSettings(), ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('Error loading barcode settings:', error);
    }
    return this.getDefaultBarcodeSettings();
  }

  /**
   * Save barcode settings to localStorage
   */
  saveBarcodeSettings(settings: BarcodeSettings): void {
    try {
      localStorage.setItem(this.BARCODE_SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving barcode settings:', error);
    }
  }

  /**
   * Get cost code settings from localStorage first, then database if not found
   */
  getCostCodeSettings(): Observable<CostCodeSettings> {
    // First try localStorage
    try {
      const saved = localStorage.getItem('costCodeLetterMapping');
      if (saved) {
        const letterMapping = JSON.parse(saved);
        return of({ letterMapping });
      }
    } catch (error) {
      console.error('Error loading cost code settings from localStorage:', error);
    }

    // If not in localStorage, try database
    return new Observable(observer => {
      this.generalSettingsService.getSettingByKey('costCodeLetterMapping').subscribe({
        next: (setting) => {
          if (setting && setting.value) {
            try {
              const letterMapping = JSON.parse(setting.value);
              // Cache to localStorage
              localStorage.setItem('costCodeLetterMapping', setting.value);
              observer.next({ letterMapping });
            } catch (error) {
              console.error('Error parsing cost code letter mapping from database:', error);
              observer.next(this.getDefaultCostCodeSettings());
            }
          } else {
            observer.next(this.getDefaultCostCodeSettings());
          }
          observer.complete();
        },
        error: (error) => {
          console.error('Error loading cost code settings from database:', error);
          observer.next(this.getDefaultCostCodeSettings());
          observer.complete();
        }
      });
    });
  }

  /**
   * Get cost code settings synchronously from localStorage (for backward compatibility)
   */
  getCostCodeSettingsSync(): CostCodeSettings {
    try {
      const saved = localStorage.getItem('costCodeLetterMapping');
      if (saved) {
        const letterMapping = JSON.parse(saved);
        return { letterMapping };
      }
    } catch (error) {
      console.error('Error loading cost code settings:', error);
    }
    return this.getDefaultCostCodeSettings();
  }

  /**
   * Save cost code settings to localStorage
   */
  saveCostCodeSettings(settings: CostCodeSettings): void {
    try {
      localStorage.setItem(this.COST_CODE_SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving cost code settings:', error);
    }
  }

  /**
   * Get default barcode settings
   */
  private getDefaultBarcodeSettings(): BarcodeSettings {
    return {
      useCostCodes: false,
      showBarcode: true,
      columns: 2,
      paperSize: '30x20'
    };
  }

  /**
   * Get default cost code settings
   */
  private getDefaultCostCodeSettings(): CostCodeSettings {
    return {
      letterMapping: {
        '0': 'A',
        '1': 'B',
        '2': 'C',
        '3': 'D',
        '4': 'E',
        '5': 'F',
        '6': 'G',
        '7': 'H',
        '8': 'I',
        '9': 'J'
      }
    };
  }

  /**
   * Get barcode settings from general settings (for backward compatibility)
   */
  getBarcodeSettingsFromGeneralSettings(): BarcodeSettings {
    try {
      // Try to get from the correct localStorage key used by general settings service
      const generalSettings = JSON.parse(localStorage.getItem('app_settings') || '{}');
      return {
        useCostCodes: generalSettings.useCostCodes === 'true',
        showBarcode: generalSettings.showBarcode === 'true',
        columns: parseInt(generalSettings.barcodePrintColumns) || 2,
        paperSize: generalSettings.barcodePaperSize || '30x20'
      };
    } catch (error) {
      console.error('Error loading settings from general settings:', error);
      return this.getDefaultBarcodeSettings();
    }
  }

  /**
   * Convert number to cost code letters based on mapping
   */
  convertNumberToCostCodeLetters(number: number): string {
    const settings = this.getCostCodeSettingsSync();
    const numberStr = number.toString();
    let result = '';

    for (let digit of numberStr) {
      result += settings.letterMapping[digit] || digit;
    }

    return result;
  }

  /**
   * Convert cost code letters back to number
   */
  convertCostCodeLettersToNumber(letters: string): number {
    const settings = this.getCostCodeSettingsSync();
    const reverseMapping: { [key: string]: string } = {};

    // Create reverse mapping
    Object.keys(settings.letterMapping).forEach(key => {
      reverseMapping[settings.letterMapping[key]] = key;
    });

    let result = '';
    for (let letter of letters) {
      result += reverseMapping[letter] || letter;
    }

    return parseInt(result, 10) || 0;
  }

  /**
   * Get CSS styles for barcode printing based on current settings
   */
  getBarcodeCSS(rows: number = 3): string {
    const settings = this.getBarcodeSettingsFromGeneralSettings();
    const columns = this.getColumnsForPaperSize(settings.paperSize);
    const fontSizes = this.getResponsiveFontSizesForPrint(settings.paperSize);
    const stickerDimensions = this.getStickerDimensions(settings.paperSize);

    return `
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        margin: 0;
        padding: 10mm;
        font-family: Arial, sans-serif;
        background: white;
      }

      .barcode-container {
        display: flex;
        flex-wrap: wrap;
        width: calc(${columns} * (${stickerDimensions.width} + 3mm) - 3mm);
        margin: 0 auto;
        gap: 3mm;
      }

      .barcode-item {
        border: 1px solid #000;
        padding: 0.5mm;
        text-align: center;
        background-color: #fff;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        width: ${stickerDimensions.width};
        height: ${stickerDimensions.height};
        flex-shrink: 0;
        overflow: hidden;
        page-break-inside: avoid;
        box-sizing: border-box;
      }

      .barcode-code {
        font-size: ${fontSizes.code}pt;
        font-weight: bold;
        font-family: 'Courier New', monospace;
        margin-bottom: 1px;
        color: #333;
        line-height: 1.1;
      }

      .barcode-name {
        margin-bottom: 1px;
        color: #666;
        word-wrap: break-word;
        line-height: 1.1;
        font-weight: bold;
        /* font-size is set dynamically based on name length */
      }

      .barcode-price {
        font-size: ${fontSizes.price}pt;
        font-weight: bold;
        color: #007bff;
        line-height: 1.1;
        margin-top: 1px;
      }

      .barcode-text {
        font-size: ${fontSizes.barcode}pt;
        margin-bottom: 1px;
        color: #666;
        border: 1px dashed #ccc;
        padding: 1px;
        background-color: #f9f9f9;
        font-family: 'Courier New', monospace;
        line-height: 1.1;
      }

      .barcode-svg-container {
        width: 100%;
        text-align: center;
        margin-bottom: 1px;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 20px;
        overflow: hidden;
      }

      .barcode-svg {
        max-width: 100%;
        height: 18px;
        display: block;
      }

      @media print {
        body {
          margin: 0;
          padding: 5mm;
        }

        .barcode-container {
          gap: 2mm;
        }

        .barcode-item {
          border: 1px solid #000 !important;
          background-color: white !important;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
      }

      @page {
        size: auto;
        margin: 0;
      }
    `;
  }

  /**
   * Get responsive font sizes for print based on paper size
   */
  private getResponsiveFontSizesForPrint(paperSize: string): { code: number, name: number, price: number, barcode: number } {
    const fontSizes = {
      '30x20': { code: 5, name: 4, price: 4, barcode: 4 },      // Very small stickers
      '33x21': { code: 6, name: 4, price: 5, barcode: 4 },      // Small stickers
      '38x25': { code: 7, name: 5, price: 6, barcode: 5 },      // Medium stickers
      '50x25': { code: 8, name: 6, price: 7, barcode: 6 },      // Medium-large stickers
      '65x15': { code: 5, name: 3, price: 4, barcode: 3 },      // Very narrow stickers
      '100x50': { code: 10, name: 8, price: 9, barcode: 8 },    // Large stickers
      '100x150': { code: 14, name: 10, price: 12, barcode: 10 } // Extra large stickers
    };
    return fontSizes[paperSize] || fontSizes['30x20'];
  }

  /**
   * Get dynamic font size for item name based on its length and paper size
   */
  private getDynamicNameFontSize(itemName: string, paperSize: string): number {
    const nameLength = (itemName || '').length;

    // Base font sizes for different paper sizes and name lengths
    const baseSizes = {
      '30x20': { short: 5, medium: 4, long: 3 },
      '33x21': { short: 6, medium: 5, long: 4 },
      '38x25': { short: 7, medium: 6, long: 5 },
      '50x25': { short: 8, medium: 7, long: 6 },
      '65x15': { short: 4, medium: 3, long: 2.5 }, // Very narrow stickers
      '100x50': { short: 10, medium: 9, long: 8 },
      '100x150': { short: 12, medium: 11, long: 10 }
    };

    const sizes = baseSizes[paperSize] || baseSizes['30x20'];

    // Determine size based on name length
    if (nameLength <= 10) {
      return sizes.short;  // Short names get larger text
    } else if (nameLength <= 20) {
      return sizes.medium; // Medium names get medium text
    } else {
      return sizes.long;   // Long names get smaller text
    }
  }

  /**
   * Get paper size CSS value
   */
  private getPaperSize(paperSize: string): string {
    const sizes = {
      '30x20': '30mm 20mm',
      '33x21': '33mm 21mm',
      '38x25': '38mm 25mm',
      '50x25': '50mm 25mm',
      '65x15': '65mm 15mm',
      '100x50': '100mm 50mm',
      '100x150': '100mm 150mm'
    };
    return sizes[paperSize] || '30mm 20mm';
  }

  /**
   * Get columns based on paper size
   */
  private getColumnsForPaperSize(paperSize: string): number {
    const columns = {
      '30x20': 3,
      '33x21': 3,
      '38x25': 2,
      '50x25': 2,
      '65x15': 1,
      '100x50': 1,
      '100x150': 1
    };
    return columns[paperSize] || 3;
  }

  /**
   * Get sticker dimensions based on paper size
   */
  private getStickerDimensions(paperSize: string): { width: string, height: string } {
    const dimensions = {
      '30x20': { width: '30mm', height: '20mm' },
      '33x21': { width: '33mm', height: '21mm' },
      '38x25': { width: '38mm', height: '25mm' },
      '50x25': { width: '50mm', height: '25mm' },
      '65x15': { width: '65mm', height: '15mm' },
      '100x50': { width: '100mm', height: '50mm' },
      '100x150': { width: '100mm', height: '150mm' }
    };
    return dimensions[paperSize] || dimensions['30x20'];
  }

  /**
   * Generate barcode HTML for printing
   */
  generateBarcodeHTML(items: any[], rows: number = 3): string {
    const settings = this.getBarcodeSettingsFromGeneralSettings();
    const costCodeSettings = this.getCostCodeSettingsSync();
    const columns = this.getColumnsForPaperSize(settings.paperSize);

    // Calculate maximum items to display based on rows and columns
    const maxItems = rows * columns;
    const itemsToDisplay = items.slice(0, maxItems);

    let itemsHTML = '';
    itemsToDisplay.forEach(item => {
      let code = '';
      if (settings.useCostCodes) {
        // Use cost code with letters if available, otherwise use original code
        if (item.itemCost && item.itemCost > 0) {
          // Use item cost to generate cost code
          const costNumber = Math.round(item.itemCost * 100);
          code = this.convertNumberToCostCodeLetters(costNumber);
        } else if (item.costCode) {
          // Fallback to existing cost code logic
          const numericPart = item.costCode.replace(/[^0-9]/g, '');
          if (numericPart) {
            const number = parseInt(numericPart, 10);
            if (!isNaN(number)) {
              const letters = this.convertNumberToCostCodeLetters(number);
              const prefix = item.costCode.replace(numericPart, '');
              code = `${prefix}${letters}`;
            } else {
              code = item.costCode;
            }
          } else {
            code = item.costCode;
          }
        } else {
          code = item.itemCode || 'N/A';
        }
      } else {
        code = item.barcode || item.itemCode;
      }

      // Generate barcode SVG or text based on settings
      let barcodeHTML = '';
      if (settings.showBarcode) {
        // Generate SVG barcode using JsBarcode
        const barcodeValue = item.barcode || item.itemCode || 'N/A';
        const uniqueId = `barcode-${Math.random().toString(36).substr(2, 9)}`;
        barcodeHTML = `
          <div class="barcode-svg-container">
            <svg class="barcode-svg" id="${uniqueId}" data-value="${barcodeValue}"></svg>
          </div>
        `;
      } else {
        // Show barcode text when barcode element is disabled
        barcodeHTML = `
          <div class="barcode-text">${item.barcode || item.itemCode || 'N/A'}</div>
        `;
      }

      // Get dynamic font size for this item's name
      const dynamicNameFontSize = this.getDynamicNameFontSize(item.itemName, settings.paperSize);

      itemsHTML += `
        <div class="barcode-item">
          ${barcodeHTML}
          <div class="barcode-code">${code}</div>
          <div class="barcode-name" style="font-size: ${dynamicNameFontSize}pt;">${item.itemName || 'Unknown Item'}</div>
          <div class="barcode-price">Rs. ${(item.sellingPrice || 0).toFixed(2)}</div>
        </div>
      `;
    });

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Barcode Print</title>
        <style>
          ${this.getBarcodeCSS(rows)}
        </style>
        <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
      </head>
      <body>
        <div class="barcode-container">
          ${itemsHTML}
        </div>
        <script>
          // Initialize all barcodes after page load
          window.addEventListener('load', function() {
            setTimeout(function() {
              const svgElements = document.querySelectorAll('.barcode-svg');
              svgElements.forEach((svg) => {
                const barcodeValue = svg.getAttribute('data-value');
                if (svg && barcodeValue && typeof JsBarcode !== 'undefined') {
                  try {
                    JsBarcode(svg, barcodeValue, {
                      format: "CODE128",
                      width: 0.8,
                      height: 18,
                      displayValue: false,
                      background: "#ffffff",
                      lineColor: "#000000",
                      margin: 0
                    });
                  } catch (e) {
                    console.error('Barcode generation error:', e);
                    // Fallback: show the barcode value as text
                    svg.outerHTML = '<div class="barcode-text">' + barcodeValue + '</div>';
                  }
                }
              });
            }, 100);
          });
        </script>
      </body>
      </html>
    `;
  }
}
