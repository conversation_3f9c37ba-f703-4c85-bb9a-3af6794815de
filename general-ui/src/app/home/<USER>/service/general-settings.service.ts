import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { tap, catchError, map } from 'rxjs/operators';
import { Settings } from '../../core/model/settings';
import { ApiConstants } from '../admin-constants';

/**
 * Service for managing general application settings
 * These settings apply to all users of the application
 */
@Injectable({
  providedIn: 'root'
})
export class GeneralSettingsService {
  private readonly SETTINGS_STORAGE_KEY = 'app_settings';
  private readonly SETTINGS_TIMESTAMP_KEY = 'app_settings_timestamp';
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  constructor(private http: HttpClient) { }

  /**
   * Save a setting to the database
   * @param setting The setting to save
   * @returns Observable with the saved setting or response object
   */
  saveSetting(setting: Settings): Observable<any> {
    return this.http.post<any>(ApiConstants.SAVE_SETTING, setting).pipe(
      tap(response => {
        console.log('Save setting response:', response);

        // The controller now returns the saved setting directly
        if (response && response.key) {
          // Direct setting object response
          const savedSetting = response as Settings;

          // Update the setting in local storage
          this.updateSettingInLocalStorage(savedSetting);
          console.log('Setting saved and updated in localStorage:', savedSetting);
        } else if (response && response.success && response.data) {
          // Fallback for response object with data
          console.log('Received response object instead of setting:', response);
        } else {
          console.warn('Unexpected response format:', response);
        }
      })
    );
  }

  /**
   * Get all settings from the database
   * @returns Observable with all settings
   */
  getAllSettings(): Observable<Settings[]> {
    // Check if we have fresh settings in local storage
    const cachedSettings = this.getSettingsFromLocalStorage();
    if (cachedSettings && this.isCacheValid()) {
      return of(cachedSettings);
    }

    // If not, fetch from the database
    return this.http.get<Settings[]>(ApiConstants.GET_ALL_GENERAL_SETTINGS).pipe(
      tap(settings => {
        // Save to local storage
        this.saveSettingsToLocalStorage(settings);
      }),
      catchError(error => {
        console.error('Error fetching settings:', error);
        // If there's an error but we have cached settings, return those
        if (cachedSettings) {
          return of(cachedSettings);
        }
        // Otherwise, return an empty array
        return of([]);
      })
    );
  }

  /**
   * Get settings by category from the database
   * @param category The category to filter by
   * @returns Observable with settings in the specified category
   */
  getSettingsByCategory(category: string): Observable<Settings[]> {
    // Fetch directly from the database
    return this.http.get<Settings[]>(ApiConstants.GET_SETTINGS_BY_CATEGORY, {
      params: { category }
    }).pipe(
      tap(settings => {
        // Update specific settings in localStorage if needed
        if (Array.isArray(settings)) {
          settings.forEach(setting => {
            // Only update localStorage for settings we care about
            if (['defaultDiscountMode', 'printerTemplate', 'useSilentPrint',
                 'minimumMarkupPercentage', 'allowSellingUnderCost'].includes(setting.key)) {
              this.updateSettingInLocalStorage(setting);
            }
          });
        }
      }),
      catchError(error => {
        console.error(`Error fetching settings for category ${category}:`, error);
        return of([]);
      })
    );
  }

  /**
   * Get a setting by key
   * @param key The key to look for
   * @returns Observable with the setting or null if not found
   */
  getSettingByKey(key: string): Observable<Settings | null> {
    // Check if we have the setting in local storage
    const cachedSettings = this.getSettingsFromLocalStorage();
    if (cachedSettings && this.isCacheValid()) {
      const setting = cachedSettings.find(s => s.key === key);
      if (setting) {
        return of(setting);
      }
    }

    // If not, fetch from the database
    return this.http.get<Settings>(ApiConstants.GET_SETTING_BY_KEY, {
      params: { key }
    }).pipe(
      tap(setting => {
        // Update this setting in local storage
        if (setting) {
          this.updateSettingInLocalStorage(setting);
        }
      }),
      catchError(error => {
        console.error(`Error fetching setting with key ${key}:`, error);
        // If there's an error but we have the cached setting, return it
        if (cachedSettings) {
          const setting = cachedSettings.find(s => s.key === key);
          if (setting) {
            return of(setting);
          }
        }
        // Otherwise, return null
        return of(null);
      })
    );
  }

  /**
   * Get a setting value by key
   * @param key The key to look for
   * @param defaultValue The default value to return if the setting is not found
   * @returns Observable with the setting value or defaultValue if not found
   */
  getSettingValue(key: string, defaultValue: string = ''): Observable<string> {
    return this.getSettingByKey(key).pipe(
      map(setting => setting ? setting.value : defaultValue),
      catchError(() => of(defaultValue))
    );
  }

  /**
   * Get a setting value by key synchronously (from local storage only)
   * @param key The key to look for
   * @param defaultValue The default value to return if the setting is not found
   * @returns The setting value or defaultValue if not found
   */
  getSettingValueSync(key: string, defaultValue: string = ''): string {
    try {
      // First try to get from the array format
      const cachedSettings = this.getSettingsFromLocalStorage();
      if (cachedSettings) {
        const setting = cachedSettings.find(s => s.key === key);
        if (setting) {
          return setting.value;
        }
      }

      // If not found in array format, try the key-value format
      const settingsStr = localStorage.getItem(this.SETTINGS_STORAGE_KEY);
      if (settingsStr) {
        const parsedSettings = JSON.parse(settingsStr);
        if (typeof parsedSettings === 'object' && parsedSettings !== null && key in parsedSettings) {
          return parsedSettings[key].toString();
        }
      }

      return defaultValue;
    } catch (error) {
      console.error(`Error getting setting value for key ${key}:`, error);
      return defaultValue;
    }
  }

  /**
   * Refresh all settings from the database
   * @returns Observable with all settings
   */
  refreshSettings(): Observable<Settings[]> {
    return this.http.get<Settings[]>(ApiConstants.GET_ALL_GENERAL_SETTINGS).pipe(
      tap(settings => {
        // Save to local storage
        this.saveSettingsToLocalStorage(settings);
      }),
      catchError(error => {
        console.error('Error refreshing settings:', error);
        return of([]);
      })
    );
  }

  /**
   * Delete a setting from the database
   * @param id The ID of the setting to delete
   * @returns Observable with the result
   */
  deleteSetting(id: string): Observable<any> {
    return this.http.delete(ApiConstants.DELETE_SETTING, {
      params: { id }
    }).pipe(
      tap(response => {
        // Check if the response has data with the deleted setting
        if (response && response.success && response.data) {
          const deletedSetting = response.data;
          if (typeof deletedSetting === 'object' && 'key' in deletedSetting) {
            // If we have the full setting object, use its key to remove from localStorage
            this.removeSettingFromLocalStorage(deletedSetting.key);
            console.log('Setting removed from localStorage:', deletedSetting.key);
          }
        }
      })
    );
  }

  /**
   * Check if the settings cache is still valid
   * @returns true if the cache is valid, false otherwise
   */
  private isCacheValid(): boolean {
    const timestamp = localStorage.getItem(this.SETTINGS_TIMESTAMP_KEY);
    if (!timestamp) {
      return false;
    }

    const timestampValue = parseInt(timestamp, 10);
    const now = Date.now();
    return now - timestampValue < this.CACHE_DURATION;
  }

  /**
   * Get settings from local storage
   * @returns Array of settings or null if not found
   */
  private getSettingsFromLocalStorage(): Settings[] | null {
    const settingsStr = localStorage.getItem(this.SETTINGS_STORAGE_KEY);
    if (!settingsStr) {
      return null;
    }

    try {
      const parsedSettings = JSON.parse(settingsStr);

      // Convert the simple key-value object to an array of Settings objects
      if (typeof parsedSettings === 'object' && parsedSettings !== null) {
        const settingsArray: Settings[] = [];

        Object.keys(parsedSettings).forEach(key => {
          const value = parsedSettings[key];
          if (value !== undefined) {
            const setting: Settings = {
              id: '', // No ID for client-side settings
              key: key,
              value: value.toString(),
              description: `User preference for ${key}`,
              category: 'UserPreference'
            };
            settingsArray.push(setting);
          }
        });

        return settingsArray.length > 0 ? settingsArray : null;
      }

      return null;
    } catch (e) {
      console.error('Error parsing settings from local storage:', e);
      return null;
    }
  }

  /**
   * Save settings to local storage
   * @param settings The settings to save
   */
  private saveSettingsToLocalStorage(settings: Settings[]): void {
    // Convert array of Settings to simple key-value object
    const simpleSettings = {};

    settings.forEach(setting => {
      if (setting && setting.key) {
        simpleSettings[setting.key] = setting.value;
      }
    });

    // Save to localStorage
    localStorage.setItem(this.SETTINGS_STORAGE_KEY, JSON.stringify(simpleSettings));
    localStorage.setItem(this.SETTINGS_TIMESTAMP_KEY, Date.now().toString());
  }

  /**
   * Update a setting in local storage
   * @param setting The setting to update
   */
  private updateSettingInLocalStorage(setting: Settings): void {
    try {
      console.log('Updating setting in localStorage:', setting);

      // Get current settings from localStorage
      const settingsStr = localStorage.getItem(this.SETTINGS_STORAGE_KEY);
      let settings = {};

      if (settingsStr) {
        try {
          settings = JSON.parse(settingsStr);
        } catch (parseError) {
          console.error('Error parsing app_settings:', parseError);
        }
      }

      // Update the setting
      settings[setting.key] = setting.value;

      // Save back to localStorage
      localStorage.setItem(this.SETTINGS_STORAGE_KEY, JSON.stringify(settings));
      localStorage.setItem(this.SETTINGS_TIMESTAMP_KEY, Date.now().toString());

      console.log('Setting updated in localStorage:', setting.key, '=', setting.value);
    } catch (error) {
      console.error('Error updating setting in local storage:', error);
    }
  }

  /**
   * Remove a setting from local storage
   * @param key The key of the setting to remove
   */
  private removeSettingFromLocalStorage(key: string): void {
    try {
      // Get current settings from localStorage
      const settingsStr = localStorage.getItem(this.SETTINGS_STORAGE_KEY);
      if (!settingsStr) {
        return;
      }

      let settings;
      try {
        settings = JSON.parse(settingsStr);
      } catch (parseError) {
        console.error('Error parsing app_settings:', parseError);
        return;
      }

      // Remove the setting
      if (settings && typeof settings === 'object') {
        delete settings[key];

        // Save back to localStorage
        localStorage.setItem(this.SETTINGS_STORAGE_KEY, JSON.stringify(settings));
        localStorage.setItem(this.SETTINGS_TIMESTAMP_KEY, Date.now().toString());

        console.log(`Removed setting from localStorage: ${key}`);
      }
    } catch (error) {
      console.error(`Error removing setting ${key} from localStorage:`, error);
    }
  }

  /**
   * Get the printer template setting
   * @returns Observable with the printer template value
   */
  getPrinterTemplate(): Observable<string> {
    return this.getSettingValue('printerTemplate', '58mm_English');
  }

  /**
   * Get the printer template setting synchronously
   * @returns The printer template value
   */
  getPrinterTemplateSync(): string {
    return this.getSettingValueSync('printerTemplate', '58mm_English');
  }
}
