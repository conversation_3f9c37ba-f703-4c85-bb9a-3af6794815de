import { Compo<PERSON>, OnInit, On<PERSON><PERSON>roy, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { PermissionsSidebarService } from '../../service/permissions-sidebar.service';

@Component({
  selector: 'app-permissions-sidebar',
  templateUrl: './permissions-sidebar.component.html',
  styleUrls: ['./permissions-sidebar.component.css']
})
export class PermissionsSidebarComponent implements OnInit, OnDestroy, AfterViewInit {

  sidebarVisible: boolean = false;
  loading: boolean = false;
  modulePermissions: Array<any> = [];

  @ViewChild('sidebarContent') sidebarContent!: ElementRef;

  private subscriptions: Subscription = new Subscription();

  constructor(
    private permissionsSidebarService: PermissionsSidebarService,
    private router: Router
  ) { }

  ngOnInit(): void {
    // Subscribe to sidebar visibility
    this.subscriptions.add(
      this.permissionsSidebarService.sidebarVisible$.subscribe(visible => {
        this.sidebarVisible = visible;
      })
    );

    // Subscribe to loading state
    this.subscriptions.add(
      this.permissionsSidebarService.loading$.subscribe(loading => {
        this.loading = loading;
      })
    );

    // Subscribe to permissions data
    this.subscriptions.add(
      this.permissionsSidebarService.permissions$.subscribe(permissions => {
        this.processPermissions(permissions);
      })
    );
  }

  ngAfterViewInit(): void {
    // Check for scroll indicators after view initialization
    setTimeout(() => this.checkScrollIndicators(), 100);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Process permissions and group by module
   */
  processPermissions(permissions: Array<any>) {
    this.modulePermissions = [];

    console.log('Sidebar processing permissions:', permissions); // Debug log

    permissions.forEach(permission => {
      let existingModule = this.modulePermissions.find(module =>
        module.name === permission.module.name
      );

      if (existingModule) {
        existingModule.perms.push({
          name: permission.name,
          route: permission.route,
          iconCss: permission.iconCss
        });
      } else {
        this.modulePermissions.push({
          name: permission.module.name,
          iconCss: this.getModuleIcon(permission.module.name), // Add module icon
          perms: [{
            name: permission.name,
            route: permission.route,
            iconCss: permission.iconCss
          }],
          expanded: false
        });
      }
    });

    console.log('Sidebar processed module permissions:', this.modulePermissions); // Debug log
  }

  /**
   * Toggle module expansion - auto-collapse other modules
   */
  toggleModule(module: any) {
    const wasExpanded = module.expanded;

    // Collapse all modules first
    this.modulePermissions.forEach(mod => {
      mod.expanded = false;
    });

    // If the clicked module was not expanded, expand it
    if (!wasExpanded) {
      module.expanded = true;
      // Check scroll indicators after expansion
      setTimeout(() => this.checkScrollIndicators(), 300);
    }
  }

  /**
   * Check if module permissions need scroll indicators
   */
  checkScrollIndicators() {
    const moduleElements = document.querySelectorAll('.module-permissions.expanded');
    moduleElements.forEach((element: any) => {
      if (element.scrollHeight > element.clientHeight) {
        element.classList.add('has-scroll');
      } else {
        element.classList.remove('has-scroll');
      }
    });
  }

  /**
   * Navigate to permission route
   */
  navigateToPermission(route: string) {
    if (route) {
      // Add /home/<USER>
      const fullRoute = `/home/<USER>
      console.log('Sidebar navigating to:', fullRoute); // Debug log
      this.router.navigateByUrl(fullRoute);
      this.closeSidebar(); // Close sidebar after navigation
    }
  }

  /**
   * Close the sidebar
   */
  closeSidebar() {
    this.permissionsSidebarService.hideSidebar();
  }

  /**
   * Get icon for module based on module name
   */
  getModuleIcon(moduleName: string): string {
    const moduleIcons = {
      'Inventory': 'fas fa-boxes text-primary',
      'Trade': 'fas fa-handshake text-success',
      'Report': 'fas fa-chart-bar text-info',
      'Admin': 'fas fa-user-cog text-warning',
      'HR': 'fas fa-users text-secondary',
      'Dashboard': 'fas fa-tachometer-alt text-danger',
      'Core': 'fas fa-cog text-dark'
    };

    return moduleIcons[moduleName] || 'fas fa-folder text-muted';
  }


}
