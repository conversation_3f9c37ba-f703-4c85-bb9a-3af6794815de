@startuml
class Action {
    String id
    MetaData type
    String reference
    String reference2
    String operator
    String change
    String remark
    LocalDateTime createdDate
    String createdBy
}
class BankAccount {
    String id
    MetaData bank
    String accountNo
    String branch
    String accountHolderName
    String remark
    boolean active
    LocalDateTime createdDate
    String createdBy
    Date lastModifiedDate
    String lastModifiedBy
}
class Company {
    String id
    String name
    String fullAddress
    String telephone1
    String telephone2
    String telephone3
    String slogan
    String logo
    String email
    String regNo
    LocalDateTime createdDate
    String createdBy
    Date lastModifiedDate
    String lastModifiedBy
}
class Expense {
    String id
    String reference
    String remark
    LocalDateTime date
    Double amount
}
class ExpenseType {
    String id
    String name
}
class LogEntry {
    String id
    LocalDateTime timestamp
    String level
    String logger
    String message
    String stackTrace
    String threadName
    LocalDateTime createdAt
}
class LoggedUser {
    String token
    Date lastLogin
}
class MetaData {
    String id
    String category
    String name
    String value
}
class Module {
    String id
    String name
    String description
    boolean activated
}
class Permission {
    String id
    String name
    String route
    boolean activated
    String iconCss
}
class Sequence {
    String id
    String name
    int counter
    String prefix
}
class Transaction {
    String id
    double amount
    String refNo
    String refType
    String thirdParty
    String operator
    String paymentMethod
    String remark
    LocalDateTime date
    LocalDate createdDate
    String createdBy
    LocalDate lastModifiedDate
    String lastModifiedBy
}
class User {
    String id
    String username
    String password
    boolean active
    String firstName
    String lastName
    String email
    Boolean enabled
    Date lastPasswordResetDate
}
class UserGroup {
    String id
    String name
    List permissions
}
class UserRole {
    String id
    RoleName name
    String displayName
}
class WorkingSection {
    String id
    String name
    String role
}
class StatRecord {
    String id
    String title
    LocalDate date
    double val1
    double val2
    double val3
    int val4
}
class Contact {
    String id
    String address1
    String address2
    String address3
    String fullAddress
    String telephone1
    String telephone2
    String email
    String web
    String fax
    MetaData contactType
}
class Department {
    String id
    String departmentName
    boolean active
}
class Designation {
    String id
    String designationName
    boolean active
}
class Employee {
    String id
    String name
    String nic
    Date dob
    String gender
    String email
    String addressLine1
    String addressLine2
    String addressLine3
    String telephone1
    String telephone2
    String userName
    Date joinedDate
    String epfNo
    String emergencyContact
    String reportingManagerId
    boolean active
}
class LeaveRequest {
    String id
    Date from
    Date to
    Employee coveringEmp
    String reason
    Leave type
    String epf
}
class Person {
    String id
    String firstName
    String lastName
    String nic
    String drivingLicenseNo
    Date dob
    String gender
    double outStanding
    String licenseFrontView
    String licenseBackView
    boolean active
}
class SalaryScale {
    String id
    String name
    double basicSalary
    double epf
    double etf
    double livingAllowance
    double mealAllowance
    double vehicleAllowance
    double specialAllowance
    double housingAllowance
    double attendanceIncentive
    double noPayDeductionBasic
    double noPayDeductionEpf
    double otRatePerHour
    double budgetaryAllowance
    boolean active
}
class Brand {
    String id
    String name
    String code
    boolean active
}
class Item {
    String id
    String itemCode
    String barcode
    String supplierCode
    String itemName
    String description
    double quantity
    double sellingPrice
    double itemCost
    double retailDiscount
    double wholesaleDiscount
    double caseQuantity
    double deadStockLevel
    boolean manageStock
    boolean active
    Date createdDate
    String createdBy
    Date lastModifiedDate
    String lastModifiedBy
}
class ItemCategory {
    String id
    String categoryName
    String code
    boolean active
}
class ItemType {
    String id
    String name
    String description
    boolean active
}
class Model {
    String id
    String name
    String code
    boolean active
}
class Rack {
    String id
    String rackNo
    String description
    boolean active
}
class Stock {
    String id
    String itemCode
    String barcode
    String itemName
    int warehouseCode
    Double quantity
    String warehouseName
    Double sellingPrice
    Double itemCost
    String categoryCode
    String brandCode
    String modelCode
    Double deadStockLevel
    boolean active
}
class StockMovement {
    String id
    String type
    String itemCode
    int whCode
    Double quantity
    Double stockCountBefore
    Double stockCountAfter
    String barcode
    String itemName
    LocalDateTime dateTime
}
class SubCategory {
    String id
    String subCategoryName
    String description
    boolean active
}
class TransferStock {
    String id
    String itemCode
    String itemName
    double sellingPrice
    Double transferQty
    int sourceWarehouseCode
    int targetWarehouseCode
    String comment
    Date createdDate
    String createdBy
    Date lastModifiedDate
    String lastModifiedBy
}
class UOM {
    String id
    String name
    boolean isWholeNumber
    String symbol
    boolean active
}
class Warehouse {
    String id
    String name
    int code
    String address
    boolean active
}
class CashDrawer {
    String id
    String counter
    Double openingBalance
    Double currentBalance
    boolean active
    LocalDate lastStartedDate
    LocalDate lastClosedDate
    LocalDateTime createdDate
    String createdBy
    Date lastModifiedDate
    String lastModifiedBy
}
class CashierHistory {
    String id
    String counter
    LocalDate date
    double openingBalance
    double closingBalance
    double cashierAmountBeforeDayEnd
    double withdrawalAmount
    double actualAmount
    double shortage
    LocalDateTime createdDate
    String createdBy
    Date lastModifiedDate
    String lastModifiedBy
}
class CashRecord {
    String id
    String counter
    Double amount
    LocalDate date
    String createdBy
}
class Cheque {
    String id
    String chequeNo
    LocalDateTime chequeDate
    String invoiceNo
    String comment
    double chequeAmount
    Date createdDate
    String createdBy
    LocalDate lastModifiedDate
    String lastModifiedBy
}
class Customer {
    String id
    String nicBr
    Double creditLimit
    String salutation
    String name
    String address
    String email
    String telephone1
    String telephone2
    Double balance
    String note
    boolean active
}
class PurchaseInvoice {
    String id
    String purchaseInvoiceNo
    String invoiceNo
    LocalDateTime date
    Double totalAmount
    Double payment
    Double balance
    LocalDate dueDate
    Double discount
    MetaData paymentMethod
    MetaData status
    LocalDateTime createdDate
    String createdBy
    Date lastModifiedDate
    String lastModifiedBy
}
class PurchaseInvoiceRecord {
    String id
    Double quantity
    Double itemCost
    Double sellingPrice
    String itemCode
    String barcode
    Double discount
    Double totalAmount
    Double subTotal
    int warehouseCode
    LocalDateTime date
}
class SalesInvoice {
    String id
    String invoiceNo
    String reference
    LocalDateTime date
    double subTotal
    double totalAmount
    double balance
    double cashBalance
    double totalDiscount
    double advancePayment
    double payment
    Cheque cheque
    double cashlessAmount
    double cashAmount
    String cardOrVoucherNo
    String chequeNo
    LocalDate dueDate
    String customerName
    LocalDateTime createdDate
    String createdBy
    Date lastModifiedDate
    String lastModifiedBy
}
class SalesInvoiceRecord {
    String id
    String itemCode
    String itemName
    String invoiceNo
    double quantity
    double itemCost
    double unitPrice
    double unitPriceOriginal
    double subTotal
    double discount
    double price
    LocalDate date
    double stockCount
    MetaData recodeType
    int warehouseCode
    String counter
    LocalDateTime createdDate
    String createdBy
    Date lastModifiedDate
    String lastModifiedBy
}
class Supplier {
    String id
    String regNo
    String name
    String address
    String telephone1
    String telephone2
    String email
    Double balance
    String remark
    String contactPersonName
    String contactPersonTelephone
    boolean active
}
Expense --> ExpenseType : type
Expense --> Employee : responsiblePerson
ExpenseType --> MetaData : category
LoggedUser --> User : user
Permission --> Module : module
Transaction --> MetaData : type
User --> List : permissions
User --> List : desktopPermissions
User --> List : userRoles
Employee --> Department : department
Employee --> SalaryScale : salaryScale
Employee --> Designation : designation
Employee --> MetaData : type
LeaveRequest --> Employee : employee
Person --> Contact : contact
Person --> MetaData : personType
Item --> ItemType : itemType
Item --> Brand : brand
Item --> Model : model
Item --> ItemCategory : itemCategory
Item --> Supplier : supplier
Item --> UOM : uom
Item --> Rack : rack
Model --> Brand : brand
SubCategory --> ItemCategory : itemCategory
CashRecord --> MetaData : purpose
CashRecord --> MetaData : type
Cheque --> MetaData : bank
Cheque --> MetaData : status
Cheque --> Customer : customer
PurchaseInvoice --> List : purchaseInvoiceRecords
PurchaseInvoice --> Supplier : supplier
PurchaseInvoiceRecord --> Item : item
SalesInvoice --> MetaData : paymentMethod
SalesInvoice --> MetaData : status
SalesInvoice --> List : salesInvoiceRecords
SalesInvoice --> Customer : customer
SalesInvoiceRecord --> Item : item
Supplier --> BankAccount : bankAccount
@enduml