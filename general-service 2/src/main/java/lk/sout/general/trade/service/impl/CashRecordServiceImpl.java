package lk.sout.general.trade.service.impl;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.User;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.UserService;
import lk.sout.general.trade.entity.CashDrawer;
import lk.sout.general.trade.entity.CashRecord;
import lk.sout.general.trade.repository.CashRecordRepository;
import lk.sout.general.trade.repository.CashDrawerRepository;
import lk.sout.general.trade.repository.CashRecordRepositoryTemplate;
import lk.sout.general.trade.service.CashRecordService;
import lk.sout.general.trade.service.CashDrawerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDate;
import java.util.List;

@Service
public class CashRecordServiceImpl implements CashRecordService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CashRecordServiceImpl.class);

    @Autowired
    CashRecordRepository cashRecordRepository;

    @Autowired
    CashRecordRepositoryTemplate cashRecordRepositoryTemplate;

    @Autowired
    CashDrawerRepository cashDrawerRepository;

    @Autowired
    CashDrawerHistoryServiceImpl cashierHistoryServiceImpl;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    CashDrawerService cashDrawerService;

    @Autowired
    UserService userService;

    @Autowired
    CashRecord cashRecord;

    @Autowired
    CashRecordService cashRecordService;

    @Transactional
    public boolean save(CashRecord cashRecord) {
        try {
            cashRecord.setId(null);
            MetaData recordTypeOut = metaDataService.searchMetaData("Cash Out", "Cash");
            MetaData recordTypeIn = metaDataService.searchMetaData("Cash In", "Cash");

            MetaData purposeDayStart = metaDataService.searchMetaData("Day Start", "CashInPurpose");
            MetaData purposeDayEnd = metaDataService.searchMetaData("Day End", "CashOutPurpose");

            if (cashRecord.getType().getId().equals(recordTypeOut.getId())) {
                cashDrawerService.deductFromCashDrawer(cashRecord.getAmount(), "1");
            }
            //skipping day start
            if (cashRecord.getType().getId().equals(recordTypeIn.getId()) &&
                    !cashRecord.getPurpose().getId().equals(purposeDayStart.getId())) {
                cashDrawerService.topUpCashDrawer(cashRecord.getAmount(), "1");
            }
            if (cashRecord.getPurpose().getId().equals(purposeDayStart.getId())) {
                CashDrawer cashDrawer = cashDrawerService.findByDrawerNo("1");
                // current balance is already added to amount
                cashDrawer.setOpeningBalance(cashRecord.getAmount());
                cashDrawer.setCurrentBalance(cashRecord.getAmount());
                cashDrawer.setLastStartedDate(LocalDate.now());
                cashDrawer.setActive(true);
                cashDrawerService.save(cashDrawer);
            }
            if (cashRecord.getPurpose().getId().equals(purposeDayEnd.getId())) {
                CashDrawer cashDrawer = cashDrawerService.findByDrawerNo("1");
                cashDrawer.setActive(false);
                cashDrawer.setLastClosedDate(cashDrawer.getLastStartedDate());
                cashDrawerService.save(cashDrawer);
            }
            cashRecordRepository.save(cashRecord);
            return true;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Saving Cash Record Failed: " + e.getMessage());
            return false;
        }
    }

    public List<CashRecord> findRecordsForToday() {
        try {
            return cashRecordRepository.findByDateBetween(LocalDate.now().atStartOfDay(),
                    LocalDate.now().plusDays(1).atStartOfDay());
        } catch (Exception e) {
            LOGGER.error("Finding Cash Records For Today Failed: " + e.getMessage());
            return null;
        }
    }

    public List<CashRecord> findRecordsBetween(LocalDate sDate, LocalDate eDate) {
        try {
            return cashRecordRepository.findByDateBetween(sDate.atStartOfDay(),
                    eDate.plusDays(1).atStartOfDay());
        } catch (Exception e) {
            LOGGER.error("Finding Cash Records between dates: " + e.getMessage());
            return null;
        }
    }

    public List<CashRecord> findByCounterAndDateBetween(String counter, LocalDate sDate, LocalDate eDate) {
        try {
            counter = (counter.length() != 1 ? counter : "0" + counter);
            return cashRecordRepository.findByCounterAndDateBetween(counter, sDate.atStartOfDay(),
                    eDate.plusDays(1).atStartOfDay());
        } catch (Exception e) {
            LOGGER.error("Finding Cash Records between dates: " + e.getMessage());
            return null;
        }
    }

    public List<CashRecord> findByCounterAndTypeAndDateBetween(String counter, String typeId, LocalDate sDate, LocalDate eDate) {
        try {
            counter = (counter.length() != 1 ? counter : "0" + counter);
            return cashRecordRepository.findByCounterAndTypeAndDateBetween(counter, typeId, sDate.atStartOfDay(),
                    eDate.plusDays(1).atStartOfDay());
        } catch (Exception e) {
            LOGGER.error("Finding Cash Records between dates: " + e.getMessage());
            return null;
        }
    }

    public List<CashRecord> findByTypeAndDateBetween(String typeId, LocalDate sDate, LocalDate eDate) {
        try {
            return cashRecordRepository.findByTypeAndDateBetween(typeId, sDate.atStartOfDay(),
                    eDate.plusDays(1).atStartOfDay());
        } catch (Exception e) {
            LOGGER.error("Finding Cash Records by type between dates: " + e.getMessage());
            return null;
        }
    }

    public List<CashRecord> findByPurposeAndCounterAndDateBetween(MetaData purpose, String counter, LocalDate sDate,
                                                                  LocalDate eDate) {
        try {
            return cashRecordRepository.findByPurposeAndCounterAndDateBetween(purpose, counter, sDate.atStartOfDay(),
                    eDate.plusDays(1).atStartOfDay());
        } catch (Exception e) {
            LOGGER.error("Finding Cash Records by purpose between dates: " + e.getMessage());
            return null;
        }
    }

    public Double calculateTotalCashOut(LocalDate date, String counter) {
        try {
            cashRecordRepositoryTemplate.calculateTotalCashOut(date, counter);
            MetaData type = metaDataService.searchMetaData("Cash Out", "Cash");
            List<CashRecord> cashRecords = cashRecordRepository.findByDateBetweenAndCounterAndType(date.atStartOfDay(),
                    date.plusDays(1).atStartOfDay(), counter, type);
            return cashRecords.stream().mapToDouble(rec -> rec.getAmount()).sum();
        } catch (Exception e) {
            LOGGER.error("Calculate total cash Out Failed: " + e.getMessage());
            return 0.0;
        }
    }

    public Double calculateCashOutWithoutDayEnd(LocalDate date, String counter) {
        try {
            cashRecordRepositoryTemplate.calculateTotalCashOut(date, counter);
            MetaData type = metaDataService.searchMetaData("Cash Out", "Cash");
            MetaData purpose = metaDataService.searchMetaData("Day End", "CashOutPurpose");
            List<CashRecord> cashRecords = cashRecordRepository.findByDateBetweenAndCounterAndTypeAndPurposeNot(date.atStartOfDay(),
                    date.plusDays(1).atStartOfDay(), counter, type, purpose);
            return cashRecords.stream().mapToDouble(rec -> rec.getAmount()).sum();
        } catch (Exception e) {
            LOGGER.error("Calculate total cash Out Failed: " + e.getMessage());
            return 0.0;
        }
    }

    public Double calculateTotalCashIn(LocalDate date, String counter) {
        try {
            cashRecordRepositoryTemplate.calculateTotalCashOut(date, counter);
            MetaData type = metaDataService.searchMetaData("Cash In", "Cash");
            List<CashRecord> cashRecords = cashRecordRepository.findByDateBetweenAndCounterAndType(date.atStartOfDay(),
                    date.plusDays(1).atStartOfDay(), counter, type);
            return cashRecords.stream().mapToDouble(rec -> rec.getAmount()).sum();
        } catch (Exception e) {
            LOGGER.error("Calculate cash In Failed: " + e.getMessage());
            return 0.0;
        }
    }

    public Double calculateOtherCashIn(LocalDate date, String counter) {
        try {
            cashRecordRepositoryTemplate.calculateTotalCashOut(date, counter);
            MetaData type = metaDataService.searchMetaData("Cash In", "Cash");
            MetaData purpose = metaDataService.searchMetaData("Day Start", "CashInPurpose");
            List<CashRecord> cashRecords = cashRecordRepository.findByDateBetweenAndCounterAndTypeAndPurposeNot(date.atStartOfDay(),
                    date.plusDays(1).atStartOfDay(), counter, type, purpose);
            return cashRecords.stream().mapToDouble(rec -> rec.getAmount()).sum();
        } catch (Exception e) {
            LOGGER.error("Calculate Other cash In Failed: " + e.getMessage());
            return 0.0;
        }
    }

    public Double getDayEndWithdrawalAmount(LocalDate date, String counter) {
        try {
            cashRecordRepositoryTemplate.calculateTotalCashOut(date, counter);
            MetaData type = metaDataService.searchMetaData("Cash Out", "Cash");
            MetaData purpose = metaDataService.searchMetaData("Day End", "CashOutPurpose");
            List<CashRecord> cashRecords = cashRecordRepository.findByDateBetweenAndCounterAndTypeAndPurpose(date.atStartOfDay(),
                    date.plusDays(1).atStartOfDay(), counter, type, purpose);
            return cashRecords.stream().mapToDouble(rec -> rec.getAmount()).sum();
        } catch (Exception e) {
            LOGGER.error("Calculate Day End cash Out Failed: " + e.getMessage());
            return 0.0;
        }
    }

    @Override
    public boolean dayStart(Double addingAmount) {
        try {
            User user = userService.findUser();
            CashDrawer cashDrawer = cashDrawerService.findByDrawerNo("1");
            MetaData recordTypeIn = metaDataService.searchMetaData("Cash In", "Cash");
            MetaData purposeDayStart = metaDataService.searchMetaData("Day Start", "CashInPurpose");
            cashRecord.setDate(LocalDate.now());
            cashRecord.setPurpose(purposeDayStart);
            cashRecord.setType(recordTypeIn);
            cashRecord.setCounter("1");
            cashRecord.setAmount(addingAmount + cashDrawer.getCurrentBalance());
            if (cashRecordService.save(cashRecord)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean addCash(Double addingAmount) {
        try {
            User user = userService.findUser();
            cashRecord.setType(metaDataService.searchMetaData("Cash In", "Cash"));
            cashRecord.setPurpose(metaDataService.searchMetaData("Adding Changes", "CashInPurpose"));
            cashRecord.setDate(LocalDate.now());
            cashRecord.setCounter("1");
            cashRecord.setAmount(addingAmount);
            if (cashRecordService.save(cashRecord)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    @Transactional
    public boolean withdraw(Double withdrawingAmount, String purposeId) {
        try {
           // User user = userService.findUser();
            CashDrawer cashDrawer = cashDrawerService.findByDrawerNo("1");
            if (cashDrawer.getCurrentBalance().compareTo(withdrawingAmount) >= 0) {
                cashRecord.setCounter("1");
                cashRecord.setDate(LocalDate.now());
                cashRecord.setPurpose(metaDataService.findById(purposeId));
                cashRecord.setAmount(withdrawingAmount);
                cashRecord.setType(metaDataService.searchMetaData("Cash Out", "Cash"));
                if (cashRecordService.save(cashRecord)) {
                    return true;
                } else {
                    return false;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return false;
    }
}
