package lk.sout.util;

/*import lk.sout.general.inventory.entity.Item;
import lk.sout.general.inventory.entity.ItemCategory;
import lk.sout.general.inventory.entity.ItemType;
import lk.sout.general.inventory.repository.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;*/
import org.springframework.stereotype.Component;

/*import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;*/

@Component
public class ExcelImporter {

   /* @Autowired
    Item item;

    public void importFromExcel(ItemRepository itemRepository, ItemType itemType,
                                UOMRepository uomRepository, ItemCategory itemCategory)
            throws IOException, InterruptedException {

        Resource resource = new ClassPathResource("item.xlsx");
        FileInputStream file = new FileInputStream(resource.getFile());
        Workbook workbook = new XSSFWorkbook(file);
        Sheet sheet = workbook.getSheetAt(0);
        Iterator<Row> rows = sheet.iterator();
        int itemcode = 10000;

        while (rows.hasNext()) {
            Row currentRow = rows.next();
            Iterator<Cell> cellsInRow = currentRow.iterator();
            Item item = new Item();
            int i = 0;
            String value;
            String barcode = null;

            while (cellsInRow.hasNext()) {
                Cell currentCell = cellsInRow.next();
                if (i == 0) {
                    item.setSupplierCode(currentCell.getStringCellValue());
                }
                if (i == 1) {
                    item.setItemCode(String.valueOf(itemcode));
                    item.setBarcode(String.valueOf(itemcode));
                    item.setItemName(currentCell.getStringCellValue());
                    item.setActive(true);
                    item.setManageStock(true);
                    item.setQuantity(0.0);
                    item.setUom(uomRepository.findByName("Nos"));
                    item.setItemType(itemType);
                    item.setItemCategory(itemCategory);
                    itemRepository.save(item);
                }
                *//*if (i == 0) {
                    if (currentCell.getCellType().equals(CellType.BLANK)) {
                        continue;
                    }
                    if (currentCell.getCellType() == CellType.NUMERIC) {
                        if (null != itemRepository.findByItemCode(String.valueOf(currentCell.
                                getNumericCellValue()))) {
                            continue;
                        }
                        item.setBarcode(String.valueOf(currentCell.getNumericCellValue()));
                    } else {
                        if (null != itemRepository.findByBarcode(currentCell.getStringCellValue())) {
                            continue;
                        }
                        item.setBarcode(currentCell.getStringCellValue());
                    }
                }
                if (i == 4) {
                    if (currentCell.getCellType() == CellType.NUMERIC) {
                        item.setItemName(String.valueOf(currentCell.getNumericCellValue()));
                    } else {
                        item.setItemName(currentCell.getStringCellValue());
                    }
                }
                *//**//*if (i == 3) {
                    item.setSellingPrice(currentCell.getNumericCellValue());
                }*//**//*
                if (i == 5) {
                    item.setItemCode(String.valueOf(itemcode));
                    item.setActive(true);
                    item.setManageStock(true);
                    item.setUom(uomRepository.findByName("Nos"));
                    itemRepository.save(item);
                }*//*
                i++;
                itemcode++;
            }
            workbook.close();
        }
    }*/

}
