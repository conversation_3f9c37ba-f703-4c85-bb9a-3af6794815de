package lk.sout.config;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Appender;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;

@Configuration
public class LoggingConfig {

    @Bean
    public Appender<ILoggingEvent> mongoDBLogAppender(MongoTemplate mongoTemplate) {
        MongoDBLogAppender mongoDBLogAppender = new MongoDBLogAppender(mongoTemplate);
        mongoDBLogAppender.setName("MONGO_APPENDER");
        mongoDBLogAppender.start();

        Logger rootLogger = (Logger) LoggerFactory.getLogger(org.slf4j.Logger.ROOT_LOGGER_NAME);
        rootLogger.addAppender(mongoDBLogAppender);
        rootLogger.setLevel(Level.ERROR);
        return mongoDBLogAppender;
    }
}
