package lk.sout.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller to check current database configuration and provide switching information
 */
@RestController
@RequestMapping("/database")
public class DatabaseSwitchController {

    @Value("${spring.data.mongodb.database:generalWeb}")
    private String currentDatabase;

    /**
     * Get current database configuration
     */
    @GetMapping("/current")
    public ResponseEntity<Map<String, Object>> getCurrentDatabase() {
        Map<String, Object> info = new HashMap<>();
        info.put("currentDatabase", currentDatabase);
        info.put("description", "All requests are using database: " + currentDatabase);
        
        Map<String, String> availableDatabases = new HashMap<>();
        availableDatabases.put("generalWeb", "Default database");
        availableDatabases.put("generalWebDemo", "Demo tenant database");
        availableDatabases.put("generalWebWanigarathna", "Wanigarathna tenant database");
        availableDatabases.put("generalWebNewcitymobile", "Newcitymobile tenant database");
        
        info.put("availableDatabases", availableDatabases);
        
        Map<String, String> switchingMethods = new HashMap<>();
        switchingMethods.put("applicationProperties", "Change spring.data.mongodb.database in application.properties");
        switchingMethods.put("systemProperty", "Start with -Dspring.data.mongodb.database=generalWebDemo");
        switchingMethods.put("environmentVariable", "Set SPRING_DATA_MONGODB_DATABASE=generalWebDemo");
        
        info.put("switchingMethods", switchingMethods);
        
        return ResponseEntity.ok(info);
    }

    /**
     * Get database switching instructions
     */
    @GetMapping("/switch-instructions")
    public ResponseEntity<Map<String, Object>> getSwitchInstructions() {
        Map<String, Object> instructions = new HashMap<>();
        
        instructions.put("currentDatabase", currentDatabase);
        
        Map<String, String> methods = new HashMap<>();
        methods.put("method1", "Edit application.properties: spring.data.mongodb.database=generalWebDemo");
        methods.put("method2", "System property: java -Dspring.data.mongodb.database=generalWebDemo -jar app.jar");
        methods.put("method3", "Environment variable: export SPRING_DATA_MONGODB_DATABASE=generalWebDemo");
        methods.put("method4", "Docker: docker run -e SPRING_DATA_MONGODB_DATABASE=generalWebDemo app");
        
        instructions.put("switchingMethods", methods);
        
        String[] availableDatabases = {
            "generalWeb",
            "generalWebDemo", 
            "generalWebWanigarathna",
            "generalWebNewcitymobile"
        };
        instructions.put("availableDatabases", availableDatabases);
        
        Map<String, String> examples = new HashMap<>();
        examples.put("demo", "spring.data.mongodb.database=generalWebDemo");
        examples.put("wanigarathna", "spring.data.mongodb.database=generalWebWanigarathna");
        examples.put("newcitymobile", "spring.data.mongodb.database=generalWebNewcitymobile");
        examples.put("default", "spring.data.mongodb.database=generalWeb");
        
        instructions.put("examples", examples);
        
        instructions.put("note", "Application restart required after changing database configuration");
        
        return ResponseEntity.ok(instructions);
    }

    /**
     * Test database connectivity info
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getDatabaseInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("configuredDatabase", currentDatabase);
        info.put("status", "Connected to: " + currentDatabase);
        info.put("type", "Single database configuration");
        info.put("multiTenancy", "Disabled - using single database");
        
        return ResponseEntity.ok(info);
    }
}
