package lk.sout.core.repository;

import lk.sout.core.entity.DailyStatistics;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * Repository for DailyStatistics entity
 */
@Repository
public interface DailyStatisticsRepository extends MongoRepository<DailyStatistics, String> {
    
    /**
     * Find statistics by date
     * @param date The date
     * @return DailyStatistics for the date or null if not found
     */
    DailyStatistics findByDate(LocalDate date);
    
    /**
     * Find statistics for a date range
     * @param startDate Start date (inclusive)
     * @param endDate End date (inclusive)
     * @return List of DailyStatistics for the date range
     */
    List<DailyStatistics> findByDateBetween(LocalDate startDate, LocalDate endDate);
    
    /**
     * Find statistics for a date range with pagination
     * @param startDate Start date (inclusive)
     * @param endDate End date (inclusive)
     * @param pageable Pagination information
     * @return Page of DailyStatistics for the date range
     */
    Page<DailyStatistics> findByDateBetween(LocalDate startDate, LocalDate endDate, Pageable pageable);
    
    /**
     * Find latest statistics
     * @param pageable Pagination information
     * @return Page of DailyStatistics ordered by date descending
     */
    Page<DailyStatistics> findAllByOrderByDateDesc(Pageable pageable);
}
