package lk.sout.core.repository;

import lk.sout.core.entity.Action;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ActionRepository extends MongoRepository<Action, String> {

    /**
     * Find actions by type
     * @param type The action type
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findAllByType(String type, Pageable pageable);

    /**
     * Find actions by reference containing the given text
     * @param reference The reference text to search for
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findAllByReferenceContainingIgnoreCase(String reference, Pageable pageable);

    /**
     * Find actions by remark containing the given text
     * @param remark The remark text to search for
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findAllByRemarkContainingIgnoreCase(String remark, Pageable pageable);

    /**
     * Find actions by created date between the given dates
     * @param startDate The start date (inclusive)
     * @param endDate The end date (inclusive)
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findAllByCreatedDateBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find actions by type and created date between the given dates
     * @param type The action type
     * @param startDate The start date (inclusive)
     * @param endDate The end date (inclusive)
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findAllByTypeAndCreatedDateBetween(String type, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find actions by reference containing the given text and created date between the given dates
     * @param reference The reference text to search for
     * @param startDate The start date (inclusive)
     * @param endDate The end date (inclusive)
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findAllByReferenceContainingIgnoreCaseAndCreatedDateBetween(String reference, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find actions by remark containing the given text and created date between the given dates
     * @param remark The remark text to search for
     * @param startDate The start date (inclusive)
     * @param endDate The end date (inclusive)
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findAllByRemarkContainingIgnoreCaseAndCreatedDateBetween(String remark, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find actions by type, reference containing the given text, and created date between the given dates
     * @param type The action type
     * @param reference The reference text to search for
     * @param startDate The start date (inclusive)
     * @param endDate The end date (inclusive)
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findAllByTypeAndReferenceContainingIgnoreCaseAndCreatedDateBetween(String type, String reference, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find actions by type, remark containing the given text, and created date between the given dates
     * @param type The action type
     * @param remark The remark text to search for
     * @param startDate The start date (inclusive)
     * @param endDate The end date (inclusive)
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findAllByTypeAndRemarkContainingIgnoreCaseAndCreatedDateBetween(String type, String remark, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);
}
