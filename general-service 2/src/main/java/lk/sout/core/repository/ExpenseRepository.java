package lk.sout.core.repository;

import lk.sout.core.entity.Expense;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ExpenseRepository extends MongoRepository<Expense, String> {

    Page<Expense> findAll(Pageable pageable);

    Page<Expense> findAllByTypeIn(List<String> typeIds, Pageable pageable);

    Page<Expense> findAllByType(String typeId, Pageable pageable);

    Page<Expense> findAllByResponsiblePerson(String empId, Pageable pageable);

    Page<Expense> findAllByResponsiblePersonAndType(String empId, String typeId, Pageable pageable);

    List<Expense> findAllByResponsiblePersonAndTypeAndDateBetween(String empId, String typeId, LocalDateTime from, LocalDateTime to);

    List<Expense> findAllByTypeAndDateBetween(String typeId, LocalDateTime from, LocalDateTime to);

    List<Expense> findAllByResponsiblePersonAndDateBetween(String empId, LocalDateTime from, LocalDateTime to);

    Page<Expense> findAllByDateBetween(LocalDateTime from, LocalDateTime to, Pageable pageable);

    List<Expense> findAllByDateBetween(LocalDateTime from, LocalDateTime to);

    Page<Expense> findAllByOrderByIdDesc(Pageable pageable);
}

