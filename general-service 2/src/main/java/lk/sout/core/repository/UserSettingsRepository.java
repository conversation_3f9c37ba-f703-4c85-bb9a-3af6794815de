package lk.sout.core.repository;

import lk.sout.core.entity.User;
import lk.sout.core.entity.UserSettings;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

/**
 * Repository for UserSettings entity
 */
@Repository
public interface UserSettingsRepository extends MongoRepository<UserSettings, String> {
    
    /**
     * Find settings by user
     * @param user The user
     * @return UserSettings for the user or null if not found
     */
    UserSettings findByUser(User user);
    
    /**
     * Find settings by username
     * @param username The username
     * @return UserSettings for the username or null if not found
     */
    UserSettings findByUser_Username(String username);
}
