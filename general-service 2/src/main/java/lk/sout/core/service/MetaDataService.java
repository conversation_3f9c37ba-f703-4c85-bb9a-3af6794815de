package lk.sout.core.service;

import lk.sout.core.entity.MetaData;
import org.springframework.stereotype.Service;

import java.util.List;

public interface MetaDataService {

    boolean save(MetaData metaData);

    boolean saveIfUnavailable(MetaData metaData);

    MetaData searchMetaData(String value, String category);

    List<MetaData> findByCategory(String text);

    MetaData findById(String id);

}
