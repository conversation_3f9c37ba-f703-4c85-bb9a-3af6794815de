package lk.sout.core.service.impl;

import lk.sout.core.entity.Action;
import lk.sout.core.entity.Response;
import lk.sout.core.repository.ActionRepository;
import lk.sout.core.service.ActionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class ActionServiceImpl implements ActionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActionService.class);

    @Autowired
    ActionRepository actionRepository;

    @Autowired
    Response response;

    @Override
    public boolean save(Action action) {
        try {
            actionRepository.save(action);
            response.setCode(200);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Creating Action Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Action Failed");
            response.setData(ex.getMessage());
            return false;
        }
    }

    @Override
    public Page<Action> findAll(Pageable pageable) {
        try {
            return actionRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Finding all actions failed: " + ex.getMessage());
            return Page.empty();
        }
    }

    @Override
    public Page<Action> findByType(String type, Pageable pageable) {
        try {
            return actionRepository.findAllByType(type, pageable);
        } catch (Exception ex) {
            LOGGER.error("Finding actions by type failed: " + ex.getMessage());
            return Page.empty();
        }
    }

    @Override
    public Page<Action> findByReferenceContaining(String reference, Pageable pageable) {
        try {
            return actionRepository.findAllByReferenceContainingIgnoreCase(reference, pageable);
        } catch (Exception ex) {
            LOGGER.error("Finding actions by reference failed: " + ex.getMessage());
            return Page.empty();
        }
    }

    @Override
    public Page<Action> findByRemarkContaining(String remark, Pageable pageable) {
        try {
            return actionRepository.findAllByRemarkContainingIgnoreCase(remark, pageable);
        } catch (Exception ex) {
            LOGGER.error("Finding actions by remark failed: " + ex.getMessage());
            return Page.empty();
        }
    }

    @Override
    public Page<Action> findByDateBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        try {
            return actionRepository.findAllByCreatedDateBetween(startDate, endDate, pageable);
        } catch (Exception ex) {
            LOGGER.error("Finding actions by date range failed: " + ex.getMessage());
            return Page.empty();
        }
    }

    @Override
    public Page<Action> findWithFilters(String type, String reference, String remark, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        try {
            // Check which filters are provided and call the appropriate repository method
            boolean hasType = type != null && !type.isEmpty();
            boolean hasReference = reference != null && !reference.isEmpty();
            boolean hasRemark = remark != null && !remark.isEmpty();
            boolean hasDateRange = startDate != null && endDate != null;

            // Apply filters based on which parameters are provided
            if (hasType && hasReference && hasDateRange) {
                return actionRepository.findAllByTypeAndReferenceContainingIgnoreCaseAndCreatedDateBetween(type, reference, startDate, endDate, pageable);
            } else if (hasType && hasRemark && hasDateRange) {
                return actionRepository.findAllByTypeAndRemarkContainingIgnoreCaseAndCreatedDateBetween(type, remark, startDate, endDate, pageable);
            } else if (hasType && hasDateRange) {
                return actionRepository.findAllByTypeAndCreatedDateBetween(type, startDate, endDate, pageable);
            } else if (hasReference && hasDateRange) {
                return actionRepository.findAllByReferenceContainingIgnoreCaseAndCreatedDateBetween(reference, startDate, endDate, pageable);
            } else if (hasRemark && hasDateRange) {
                return actionRepository.findAllByRemarkContainingIgnoreCaseAndCreatedDateBetween(remark, startDate, endDate, pageable);
            } else if (hasDateRange) {
                return actionRepository.findAllByCreatedDateBetween(startDate, endDate, pageable);
            } else if (hasType) {
                return actionRepository.findAllByType(type, pageable);
            } else if (hasReference) {
                return actionRepository.findAllByReferenceContainingIgnoreCase(reference, pageable);
            } else if (hasRemark) {
                return actionRepository.findAllByRemarkContainingIgnoreCase(remark, pageable);
            } else {
                // No filters provided, return all actions
                return actionRepository.findAll(pageable);
            }
        } catch (Exception ex) {
            LOGGER.error("Finding actions with filters failed: " + ex.getMessage());
            return Page.empty();
        }
    }
}
