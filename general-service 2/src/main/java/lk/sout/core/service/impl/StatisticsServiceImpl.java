package lk.sout.core.service.impl;

import lk.sout.core.entity.DailyStatistics;
import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Transaction;
import lk.sout.core.repository.DailyStatisticsRepository;
import lk.sout.core.repository.MetaDataRepository;
import lk.sout.core.repository.TransactionRepository;
import lk.sout.core.service.StatisticsService;
import lk.sout.general.inventory.entity.Stock;
import lk.sout.general.inventory.entity.StockSummary;
import lk.sout.general.inventory.repository.CustomStockRepository;
import lk.sout.general.inventory.repository.StockRepository;
import lk.sout.general.trade.entity.Cheque;
import lk.sout.general.trade.entity.SalesInvoice;
import lk.sout.general.trade.entity.SalesInvoiceRecord;
import lk.sout.general.trade.repository.ChequeRepository;
import lk.sout.general.trade.repository.SalesInvoiceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of StatisticsService
 */
@Service
public class StatisticsServiceImpl implements StatisticsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StatisticsServiceImpl.class);

    @Autowired
    private DailyStatisticsRepository dailyStatisticsRepository;

    @Autowired
    private StockRepository stockRepository;

    @Autowired
    private CustomStockRepository customStockRepository;

    @Autowired
    private SalesInvoiceRepository salesInvoiceRepository;

    @Autowired
    private ChequeRepository chequeRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private MetaDataRepository metaDataRepository;

    @Override
    public DailyStatistics generateDailyStatistics() {
        return generateStatisticsForDate(LocalDate.now());
    }

    @Override
    public DailyStatistics generateStatisticsForDate(LocalDate date) {
        try {
            LOGGER.info("Generating statistics for date: {}", date);

            // Check if statistics already exist for this date
            DailyStatistics existingStats = dailyStatisticsRepository.findByDate(date);
            DailyStatistics statistics = existingStats != null ? existingStats : new DailyStatistics(date);

            // Calculate various statistics
            calculateStockStatistics(statistics);
            calculateSalesStatistics(statistics, date);
            calculatePendingBillsStatistics(statistics);
            calculateChequeStatistics(statistics);
            calculateTransactionStatistics(statistics, date);

            // Save and return the statistics
            dailyStatisticsRepository.save(statistics);
            LOGGER.info("Statistics generation completed for date: {}", date);
            return statistics;
        } catch (Exception e) {
            LOGGER.error("Error generating statistics for date {}: {}", date, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public DailyStatistics findByDate(LocalDate date) {
        try {
            return dailyStatisticsRepository.findByDate(date);
        } catch (Exception e) {
            LOGGER.error("Error finding statistics for date {}: {}", date, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<DailyStatistics> findByDateRange(LocalDate startDate, LocalDate endDate) {
        try {
            return dailyStatisticsRepository.findByDateBetween(startDate, endDate);
        } catch (Exception e) {
            LOGGER.error("Error finding statistics for date range {} to {}: {}", 
                    startDate, endDate, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Page<DailyStatistics> findByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable) {
        try {
            return dailyStatisticsRepository.findByDateBetween(startDate, endDate, pageable);
        } catch (Exception e) {
            LOGGER.error("Error finding statistics for date range {} to {} with pagination: {}", 
                    startDate, endDate, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Page<DailyStatistics> findLatest(Pageable pageable) {
        try {
            return dailyStatisticsRepository.findAllByOrderByDateDesc(pageable);
        } catch (Exception e) {
            LOGGER.error("Error finding latest statistics: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean save(DailyStatistics statistics) {
        try {
            // Ensure netCashFlow is calculated correctly
            if (statistics.getTotalIncome() != 0 || statistics.getTotalExpense() != 0) {
                statistics.setNetCashFlow(statistics.getTotalIncome() - statistics.getTotalExpense());
            }

            dailyStatisticsRepository.save(statistics);
            return true;
        } catch (Exception e) {
            LOGGER.error("Error saving statistics: {}", e.getMessage(), e);
            return false;
        }
    }


    @Override
    public void calculateStockStatistics(DailyStatistics statistics) {
        try {
            // Get all stocks
            List<Stock> stocks = stockRepository.findAll();

            // Calculate total stock value, cost, and quantity
            double totalValue = 0;
            double totalCost = 0;
            double totalQuantity = 0;

            for (Stock stock : stocks) {
                if (stock.getSellingPrice() != null && stock.getQuantity() != null) {
                    totalValue += stock.getSellingPrice() * stock.getQuantity();
                }

                if (stock.getItemCost() != null && stock.getQuantity() != null) {
                    totalCost += stock.getItemCost() * stock.getQuantity();
                }

                if (stock.getQuantity() != null) {
                    totalQuantity += stock.getQuantity();
                }
            }

            // Set stock statistics
            statistics.setTotalStockValue(totalValue);
            statistics.setTotalStockCost(totalCost);
            statistics.setTotalStockItems(stocks.size());
            statistics.setTotalStockQuantity(totalQuantity);
            statistics.setEstimatedProfit(totalValue - totalCost);

            // Try to get warehouse-specific statistics
            try {
                List<StockSummary> stockSummaries = customStockRepository.findStockSummary();
                if (stockSummaries != null && !stockSummaries.isEmpty()) {
                    for (int i = 0; i < stockSummaries.size(); i++) {
                        StockSummary summary = stockSummaries.get(i);
                        String warehouseName = summary.getWarehouseName() != null ? 
                                summary.getWarehouseName() : "Warehouse " + i;

                        statistics.addAdditionalStat("stockValue_" + warehouseName, summary.getTotalPriceValue());
                        statistics.addAdditionalStat("stockCost_" + warehouseName, summary.getTotalCostValue());
                        statistics.addAdditionalStat("stockItems_" + warehouseName, (double) summary.getNoOfItems());
                        statistics.addAdditionalStat("stockQuantity_" + warehouseName, summary.getTotalQuantity());
                    }
                }
            } catch (Exception e) {
                LOGGER.warn("Error calculating warehouse-specific stock statistics: {}", e.getMessage());
            }

        } catch (Exception e) {
            LOGGER.error("Error calculating stock statistics: {}", e.getMessage(), e);
        }
    }

    @Override
    public void calculateSalesStatistics(DailyStatistics statistics, LocalDate date) {
        try {
            // Get start and end of the day
            LocalDateTime startOfDay = date.atStartOfDay();
            LocalDateTime endOfDay = date.atTime(LocalTime.MAX);

            // Get sales invoices for the day
            List<SalesInvoice> invoices = salesInvoiceRepository.findAllByDateBetween(startOfDay, endOfDay);

            if (invoices == null || invoices.isEmpty()) {
                statistics.setDailySales(0);
                statistics.setDailyInvoiceCount(0);
                statistics.setDailyProfit(0);
                statistics.setAverageInvoiceValue(0);
                return;
            }

            // Calculate daily sales
            double totalSales = 0;
            double totalProfit = 0;

            for (SalesInvoice invoice : invoices) {
                totalSales += invoice.getTotalAmount();

                // Calculate profit for this invoice
                if (invoice.getSalesInvoiceRecords() != null) {
                    for (SalesInvoiceRecord record : invoice.getSalesInvoiceRecords()) {
                        double cost = record.getItemCost() != 0 ? record.getItemCost() : 0;
                        double price = record.getUnitPrice() != 0 ? record.getUnitPrice() : 0;
                        double quantity = record.getQuantity() != 0 ? record.getQuantity() : 0;

                        totalProfit += (price - cost) * quantity;
                    }
                }
            }

            // Set sales statistics
            statistics.setDailySales(totalSales);
            statistics.setDailyInvoiceCount(invoices.size());
            statistics.setDailyProfit(totalProfit);
            statistics.setAverageInvoiceValue(invoices.size() > 0 ? totalSales / invoices.size() : 0);

        } catch (Exception e) {
            LOGGER.error("Error calculating sales statistics: {}", e.getMessage(), e);
        }
    }

    @Override
    public void calculatePendingBillsStatistics(DailyStatistics statistics) {
        try {
            // Find pending status metadata
            MetaData pendingStatus = metaDataRepository.findByValueAndCategory("Pending", "InvoiceStatus");

            if (pendingStatus == null) {
                LOGGER.warn("Pending status metadata not found");
                return;
            }

            // Get pending invoices
            List<SalesInvoice> pendingInvoices = salesInvoiceRepository.findAllByStatus(pendingStatus, null).getContent();

            if (pendingInvoices == null || pendingInvoices.isEmpty()) {
                statistics.setTotalPendingBills(0);
                statistics.setPendingBillCount(0);
                statistics.setOldestPendingBillAge(0);
                return;
            }

            // Calculate pending bills statistics
            double totalPendingAmount = 0;
            LocalDateTime oldestDate = LocalDateTime.now();

            for (SalesInvoice invoice : pendingInvoices) {
                totalPendingAmount += invoice.getBalance();

                if (invoice.getDate() != null && invoice.getDate().isBefore(oldestDate)) {
                    oldestDate = invoice.getDate();
                }
            }

            // Calculate age of oldest pending bill in days
            long oldestAgeDays = ChronoUnit.DAYS.between(oldestDate, LocalDateTime.now());

            // Set pending bills statistics
            statistics.setTotalPendingBills(totalPendingAmount);
            statistics.setPendingBillCount(pendingInvoices.size());
            statistics.setOldestPendingBillAge(oldestAgeDays);

        } catch (Exception e) {
            LOGGER.error("Error calculating pending bills statistics: {}", e.getMessage(), e);
        }
    }

    @Override
    public void calculateChequeStatistics(DailyStatistics statistics) {
        try {
            // Find cheque status metadata
            MetaData pendingStatus = metaDataRepository.findByValueAndCategory("Pending", "ChequeStatus");
            MetaData depositedStatus = metaDataRepository.findByValueAndCategory("Deposited", "ChequeStatus");

            if (pendingStatus == null || depositedStatus == null) {
                LOGGER.warn("Cheque status metadata not found");
                return;
            }

            // Get pending and deposited cheques
            List<Cheque> pendingCheques = chequeRepository.findAllByStatusId(pendingStatus.getId());
            List<Cheque> depositedCheques = chequeRepository.findAllByStatusId(depositedStatus.getId());

            // Calculate pending cheques statistics
            double totalPendingAmount = 0;
            if (pendingCheques != null) {
                for (Cheque cheque : pendingCheques) {
                    totalPendingAmount += cheque.getChequeAmount();
                }
            }

            // Calculate deposited cheques statistics
            double totalDepositedAmount = 0;
            if (depositedCheques != null) {
                for (Cheque cheque : depositedCheques) {
                    totalDepositedAmount += cheque.getChequeAmount();
                }
            }

            // Set cheque statistics
            statistics.setTotalPendingCheques(totalPendingAmount);
            statistics.setPendingChequeCount(pendingCheques != null ? pendingCheques.size() : 0);
            statistics.setTotalDepositedCheques(totalDepositedAmount);
            statistics.setDepositedChequeCount(depositedCheques != null ? depositedCheques.size() : 0);

        } catch (Exception e) {
            LOGGER.error("Error calculating cheque statistics: {}", e.getMessage(), e);
        }
    }

    @Override
    public void calculateTransactionStatistics(DailyStatistics statistics, LocalDate date) {
        try {
            // Get start and end of the day
            LocalDateTime startOfDay = date.atStartOfDay();
            LocalDateTime endOfDay = date.atTime(LocalTime.MAX);

            // Get transactions for the day
            List<Transaction> transactions = transactionRepository.findAllByDateBetween(startOfDay, endOfDay);

            if (transactions == null || transactions.isEmpty()) {
                statistics.setTotalIncome(0);
                statistics.setTotalExpense(0);
                statistics.setNetCashFlow(0);
                return;
            }

            // Calculate income and expense
            double totalIncome = 0;
            double totalExpense = 0;

            for (Transaction transaction : transactions) {
                if (transaction.getType() != null && transaction.getType().getValue() != null) {
                    if (transaction.getType().getValue().equalsIgnoreCase("Income")) {
                        totalIncome += transaction.getAmount();
                    } else if (transaction.getType().getValue().equalsIgnoreCase("Expense")) {
                        totalExpense += transaction.getAmount();
                    }
                }
            }

            // Set transaction statistics
            statistics.setTotalIncome(totalIncome);
            statistics.setTotalExpense(totalExpense);
            statistics.setNetCashFlow(totalIncome - totalExpense);

            // Add transaction type statistics
            try {
                // Group transactions by type
                if (transactions != null && !transactions.isEmpty()) {
                    // Filter transactions with non-null type
                    List<Transaction> validTransactions = transactions.stream()
                            .filter(t -> t.getType() != null && t.getType().getValue() != null)
                            .collect(Collectors.toList());

                    // Group by type value and sum amounts
                    validTransactions.stream()
                            .collect(Collectors.groupingBy(
                                    t -> t.getType().getValue(),
                                    Collectors.summingDouble(Transaction::getAmount)))
                            .forEach((type, amount) -> {
                                statistics.addAdditionalStat("transaction_" + type, amount);
                            });
                }
            } catch (Exception e) {
                LOGGER.warn("Error calculating transaction type statistics: {}", e.getMessage());
            }

        } catch (Exception e) {
            LOGGER.error("Error calculating transaction statistics: {}", e.getMessage(), e);
        }
    }
}
