package lk.sout.core.service;


import lk.sout.core.entity.Sequence;
import org.springframework.data.domain.Pageable;

public interface SequenceService {

    boolean save(Sequence sequence);

    /**
     * Save a sequence if it doesn't already exist
     * @param name The name of the sequence
     * @param prefix The prefix to use for the sequence
     * @param initialValue The initial counter value
     * @return true if successful, false otherwise
     */
    boolean saveIfUnavailable(String name, String prefix, int initialValue);

    Iterable<Sequence> findAll(Pageable pageable);

    Sequence findSequenceByName(String name);

    boolean incrementSequence(String name);

}
