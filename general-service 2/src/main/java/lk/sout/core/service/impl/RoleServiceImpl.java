package lk.sout.core.service.impl;

import lk.sout.core.entity.UserRole;
import lk.sout.core.repository.UserRoleRepository;
import lk.sout.core.service.RoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RoleServiceImpl implements RoleService {

    final static Logger LOGGER = LoggerFactory.getLogger(RoleServiceImpl.class);

    @Autowired
    private UserRoleRepository roleRepository;

    @Override
    public List<UserRole> findAll(){
        try{
            return roleRepository.findAll();
        }catch (Exception ex){
            LOGGER.error("roles retrieving failed" + ex.getMessage());
            return null;
        }
    }

    @Override
    public String save(UserRole role) {
        try {
            roleRepository.save(role);
            return "success";
        } catch (Exception ex) {
            LOGGER.error("create role failed " + ex.getMessage());
            return ex.getMessage();
        }
    }

    @Override
    public String delete(String id) {
        try{
            roleRepository.deleteById(id);
            return "success";
        }catch (Exception ex){
            LOGGER.error("create role failed " + ex.getMessage());
            return ex.getMessage();
        }
    }

    @Override
    public UserRole search(String name) {
        try{
            return    roleRepository.findByName(name);

        }catch (Exception ex){
            return null;
        }
    }

}
