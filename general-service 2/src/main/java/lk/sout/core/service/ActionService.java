package lk.sout.core.service;

import lk.sout.core.entity.Action;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;

public interface ActionService {
    /**
     * Save an action
     * @param action The action to save
     * @return True if the action was saved successfully, false otherwise
     */
    boolean save(Action action);

    /**
     * Find all actions with pagination
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findAll(Pageable pageable);

    /**
     * Find actions by type with pagination
     * @param type The action type
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findByType(String type, Pageable pageable);

    /**
     * Find actions by reference containing the given text with pagination
     * @param reference The reference text to search for
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findByReferenceContaining(String reference, Pageable pageable);

    /**
     * Find actions by remark containing the given text with pagination
     * @param remark The remark text to search for
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findByRemarkContaining(String remark, Pageable pageable);

    /**
     * Find actions by created date between the given dates with pagination
     * @param startDate The start date (inclusive)
     * @param endDate The end date (inclusive)
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findByDateBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find actions with multiple filters
     * @param type The action type (optional)
     * @param reference The reference text to search for (optional)
     * @param remark The remark text to search for (optional)
     * @param startDate The start date (optional)
     * @param endDate The end date (optional)
     * @param pageable Pagination information
     * @return Page of actions
     */
    Page<Action> findWithFilters(String type, String reference, String remark, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);
}
