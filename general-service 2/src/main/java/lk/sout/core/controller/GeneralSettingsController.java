package lk.sout.core.controller;

import lk.sout.core.entity.Response;
import lk.sout.core.entity.Settings;
import lk.sout.core.service.GeneralSettingsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing general application settings
 * These settings apply to all users of the application
 */
@RestController
@RequestMapping("/settings")
public class GeneralSettingsController {

    private static final Logger LOGGER = LoggerFactory.getLogger(GeneralSettingsController.class);

    @Autowired
    private GeneralSettingsService generalSettingsService;

    /**
     * Save a setting
     * @param settings The setting to save
     * @return Response with the saved setting
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<?> save(@RequestBody Settings settings) {
        try {
            Response response = generalSettingsService.save(settings);

            if (!response.isSuccess()) {
                // If there was an error, return the error response
                return ResponseEntity.status(response.getCode()).body(response);
            }

            // If successful, get the saved setting by ID and return it
            String savedId = (String) response.getData();
            if (savedId != null) {
                Settings savedSetting = generalSettingsService.findById(savedId);
                if (savedSetting != null) {
                    return ResponseEntity.ok(savedSetting);
                }
            }

            // Fallback to returning the response if we couldn't get the saved setting
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            LOGGER.error("Error saving setting: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find all settings
     * @return List of all settings
     */
    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAll() {
        try {
            return ResponseEntity.ok(generalSettingsService.findAll());
        } catch (Exception e) {
            LOGGER.error("Error finding all settings: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find settings by category
     * @param category The category to filter by
     * @return List of settings in the category
     */
    @RequestMapping(value = "/findByCategory", method = RequestMethod.GET)
    public ResponseEntity<?> findByCategory(@RequestParam String category) {
        try {
            LOGGER.debug("Finding settings by category: {}", category);
            return ResponseEntity.ok(generalSettingsService.findByCategory(category));
        } catch (Exception e) {
            LOGGER.error("Error finding settings by category {}: {}", category, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find a setting by key
     * @param key The key to search for
     * @return The setting if found
     */
    @RequestMapping(value = "/findByKey", method = RequestMethod.GET)
    public ResponseEntity<?> findByKey(@RequestParam String key) {
        try {
            LOGGER.debug("Finding setting by key: {}", key);
            Settings setting = generalSettingsService.findByKey(key);
            if (setting == null) {
                LOGGER.warn("Setting not found with key: {}", key);
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(setting);
        } catch (Exception e) {
            LOGGER.error("Error finding setting by key {}: {}", key, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error: " + e.getMessage());
        }
    }

    /**
     * Delete a setting
     * @param id The ID of the setting to delete
     * @return Response with the result
     */
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public ResponseEntity<?> delete(@RequestParam String id) {
        try {
            return ResponseEntity.ok(generalSettingsService.delete(id));
        } catch (Exception e) {
            LOGGER.error("Error deleting setting with id {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Initialize default settings
     * @return Success message
     */
    @RequestMapping(value = "/initializeDefaults", method = RequestMethod.POST)
    public ResponseEntity<?> initializeDefaults() {
        try {
            generalSettingsService.initializeDefaultSettings();
            return ResponseEntity.ok("Default settings initialized successfully");
        } catch (Exception e) {
            LOGGER.error("Error initializing default settings: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
