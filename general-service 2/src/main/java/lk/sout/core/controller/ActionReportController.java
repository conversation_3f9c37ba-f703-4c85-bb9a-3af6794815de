package lk.sout.core.controller;

import lk.sout.core.entity.Action;
import lk.sout.core.entity.MetaData;
import lk.sout.core.service.ActionService;
import lk.sout.core.service.MetaDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * Controller for Action Report
 */
@RestController
@RequestMapping("/actionReport")
public class ActionReportController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActionReportController.class);

    @Autowired
    private ActionService actionService;

    @Autowired
    private MetaDataService metaDataService;

    /**
     * Get all actions with pagination
     * @param page Page number (0-based)
     * @param pageSize Number of items per page
     * @return Page of actions
     */
    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAll(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.DESC, "createdDate"));
            Page<Action> actions = actionService.findAll(pageable);
            return ResponseEntity.ok(actions);
        } catch (Exception e) {
            LOGGER.error("Error finding all actions: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get all action types (MetaData with category "Action")
     * @return List of action types
     */
    @RequestMapping(value = "/getActionTypes", method = RequestMethod.GET)
    public ResponseEntity<?> getActionTypes() {
        try {
            List<MetaData> actionTypes = metaDataService.findByCategory("Action");
            return ResponseEntity.ok(actionTypes);
        } catch (Exception e) {
            LOGGER.error("Error finding action types: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find actions with filters
     * @param type Action type (optional)
     * @param reference Reference text to search for (optional)
     * @param remark Remark text to search for (optional)
     * @param startDate Start date for date range filter (optional)
     * @param endDate End date for date range filter (optional)
     * @param page Page number (0-based)
     * @param pageSize Number of items per page
     * @return Page of filtered actions
     */
    @RequestMapping(value = "/findWithFilters", method = RequestMethod.GET)
    public ResponseEntity<?> findWithFilters(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String reference,
            @RequestParam(required = false) String remark,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            // Convert LocalDate to LocalDateTime if dates are provided
            LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : null;
            LocalDateTime endDateTime = endDate != null ? endDate.atTime(LocalTime.MAX) : null;

            Pageable pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.DESC, "createdDate"));
            Page<Action> actions = actionService.findWithFilters(type, reference, remark, startDateTime, endDateTime, pageable);
            return ResponseEntity.ok(actions);
        } catch (Exception e) {
            LOGGER.error("Error finding actions with filters: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
