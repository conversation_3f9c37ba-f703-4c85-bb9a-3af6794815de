package lk.sout.core.controller;

import lk.sout.core.entity.MetaData;
import lk.sout.core.service.MetaDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/metaData")
public class MetaDataController {

    @Autowired
    MetaDataService metaDataService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody MetaData metaData) {
        try {
            return ResponseEntity.ok(metaDataService.save(metaData));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByCategory", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByCategory(@RequestParam("any") String any) {
        try {
            return ResponseEntity.ok(metaDataService.findByCategory(any));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


    @RequestMapping(value = "/findMetaDataByCatVal", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("value") String value, @RequestParam("category")String cate) {
        try {
            return ResponseEntity.ok(metaDataService.searchMetaData(value,cate));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findMetaDataByCatValBool", method = RequestMethod.GET)
    private ResponseEntity<?> findMetaDataByCatValBool(@RequestParam("value") String value, @RequestParam("category")String cate) {
        try {
            return ResponseEntity.ok(metaDataService.searchMetaData(value,cate));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET)
    private ResponseEntity<?> findById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(metaDataService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
