package lk.sout.core.controller;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Transaction;
import lk.sout.core.service.TransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@RestController
@RequestMapping("/transaction")
public class TransactionController {

    @Autowired
    TransactionService transactionsService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Transaction transaction) {
        try {
            return ResponseEntity.ok(transactionsService.save(transaction));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(transactionsService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByCreatedAtBetween", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByCreatedAtBetween(@RequestParam("startDate") LocalDate startDate,
                                                        @RequestParam("endDate") LocalDate endDate) {
        try {
            return ResponseEntity.ok(transactionsService.findAllByDateBetween(startDate, endDate));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchByType", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByType(@RequestBody MetaData type) {
        try {
            return ResponseEntity.ok(transactionsService.findByType(type));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchByThirdParty", method = RequestMethod.GET)
    private ResponseEntity<?> searchByThirdParty(@RequestParam("thirdParty") String thirdParty) {
        try {
            return ResponseEntity.ok(transactionsService.findByThirdParty(thirdParty));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByRef", method = RequestMethod.GET)
    private ResponseEntity<?> findByRef(@RequestParam("refNo") String refNo) {
        try {
            return ResponseEntity.ok(transactionsService.findByRefNo(refNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByRangeFilterAndOperator", method = RequestMethod.GET)
    private ResponseEntity<?> findByRangeFilterAndOperator(@RequestParam("rangeId") String rangeId,
                                                           @RequestParam("operator") String operator) {
        try {
            return ResponseEntity.ok(transactionsService.findByRangeFilterAndOperator(rangeId, operator));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByDateRangeAndOperator", method = RequestMethod.GET)
    private ResponseEntity<?> findByDateRangeAndOperator(@RequestParam("sDate") String sDate,
                                                         @RequestParam("eDate") String eDate,
                                                         @RequestParam("operator") String operator) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            // Use the correct method based on the operator parameter
            if (operator.equals("all")) {
                return ResponseEntity.ok(transactionsService.findAllByDateBetween(LocalDate.parse(sDate, formatter),
                        LocalDate.parse(eDate, formatter)));
            } else {
                return ResponseEntity.ok(transactionsService.findByDateRangeAndOperator(LocalDate.parse(sDate, formatter),
                        LocalDate.parse(eDate, formatter), operator));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
