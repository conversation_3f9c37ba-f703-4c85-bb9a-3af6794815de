# Fixing the Duplicate Key Error for Customer Routes

## Problem

When saving a customer without a route, you may encounter the following error:

```
E11000 duplicate key error collection: generalWebAjStore.customer index: route.routeNo dup key: { route.routeNo: null }
```

This happens because MongoDB has created a unique index on the `route.routeNo` field, and it treats all `null` values as duplicates.

## Solution

There are two parts to the solution:

### 1. Code Fix

The code has been updated in `CustomerServiceImpl.java` to properly handle null routes by ensuring that if a route object exists but has no routeNo, the entire route is set to null.

### 2. Database Fix

You need to drop the problematic index from the MongoDB database. Follow these steps:

1. Connect to your MongoDB server
2. Run the provided script:

```bash
mongo generalWebAjStore drop_route_index.js
```

Or manually drop the index:

```javascript
use generalWebAjStore
db.customer.dropIndex("route.routeNo_1")
```

## Verification

After applying both fixes, you should be able to save customers without a route without encountering the duplicate key error.

## Prevention

To prevent this issue in the future:

1. Always ensure that route objects are properly initialized or set to null
2. Avoid creating unique indexes on fields that can be null in multiple documents
3. Consider using sparse indexes if you need uniqueness only for non-null values
